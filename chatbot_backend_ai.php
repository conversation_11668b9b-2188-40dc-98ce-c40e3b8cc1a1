<?php
// --- DATABASE CONNECTION & CHATBOT LOGIC ---

// 1. Database Credentials
// IMPORTANT: Your teammate will change these for the GoDaddy server.
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "aiinfoways_db"; 

// 2. <PERSON>reate and Check Connection
$con = mysqli_connect($servername, $username, $password, $dbname);
if (!$con) {
    header('Content-Type: application/json');
    echo json_encode(['answer' => 'Error: Database connection failed.']);
    exit();
}

// 3. Set Header and Read Input
header('Content-Type: application/json');
$data = json_decode(file_get_contents('php://input'), true);
$userMessage = $data['question'] ?? '';

// 4. Prepare a Default Response
$responseText = "I'm sorry, I don't have an answer for that. Please try another question.";

// 5. Query the Database
if (!empty($userMessage)) {
    $sanitizedMessage = mysqli_real_escape_string($con, strtolower($userMessage));
    
    // This smart query looks for whole words to prevent incorrect matches
    $searchPattern = '[[:<:]]' . preg_replace('/\s+/', '[[:>:]]|[[:<:]]', $sanitizedMessage) . '[[:>:]]';

    $sql = "SELECT replies FROM chatbot_aix WHERE queries REGEXP '$searchPattern' LIMIT 1";
    
    $result = mysqli_query($con, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $responseText = $row['replies'];
    }
}

// 6. Send the Response
echo json_encode(['answer' => $responseText]);
mysqli_close($con);
?>