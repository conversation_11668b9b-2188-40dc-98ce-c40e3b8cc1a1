<!DOCTYPE HTML>
<!--
	Ethereal by HTML5 UP
	html5up.net | @ajlkn
	Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
-->
<html>
	<head>
		<title>AI Infoways Inc.</title>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
		<link rel="stylesheet" href="assets/css/main.css" />
		<noscript><link rel="stylesheet" href="assets/css/noscript.css" /></noscript>
		
		      <!-- ChatBot Code starts Here -->
			   <style>
            #chat-widget-container { position: fixed; bottom: 20px; right: 20px; z-index: 1000; display: flex; flex-direction: column; align-items: center; }
            #chat-bubble { width: 60px; height: 60px; background-color: #2563eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; box-shadow: 0 4px 12px rgba(0,0,0,0.3); transition: transform 0.2s; }
            #chat-bubble:hover { transform: scale(1.1); }
            #chat-popup { width: 350px; height: 500px; background-color: #fff; border-radius: 15px; box-shadow: 0 5px 25px rgba(0,0,0,0.3); overflow: hidden; display: none; flex-direction: column; margin-bottom: 10px; }
            #chat-popup.open { display: flex; }
            .chat-header { background-color: #2563eb; color: white; padding: 12px; display: flex; justify-content: space-between; align-items: center; }
            .chat-header h3 { margin: 0; font-size: 1.1em; }
            #chat-close-btn { background: none; border: none; color: white; font-size: 24px; cursor: pointer; }
            .chat-box { flex-grow: 1; padding: 15px; overflow-y: auto; background-color: #f4f7f6; display: flex; flex-direction: column; }
            .chat-message { max-width: 80%; padding: 10px; margin-bottom: 10px; border-radius: 15px; line-height: 1.4; word-wrap: break-word; }
            .user-message { background-color: #d1e7fd; color: #000; align-self: flex-end; }
            .bot-message { background-color: #e2e3e5; color: #000; align-self: flex-start; }
            .chat-footer { padding: 10px; display: flex; border-top: 1px solid #ddd; }
            #chat-input { flex-grow: 1; padding: 10px; border: 1px solid #ccc; border-radius: 20px; outline: none; color: #000; }
            #chat-send-btn { background: #2563eb; color: white; border: none; border-radius: 20px; padding: 10px 15px; margin-left: 10px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 500; }

            /* Simple Ask Me Speech Bubble */
            .ask-me-bubble { position: relative; margin-bottom: 10px; background: white; color: #374151; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: 500; white-space: nowrap; box-shadow: 0 2px 8px rgba(0,0,0,0.1); font-family: 'Arial', sans-serif; transition: opacity 0.3s ease; }
            .ask-me-bubble::after { content: ''; position: absolute; top: 100%; left: 50%; transform: translateX(-50%); border: 6px solid transparent; border-top-color: white; }
            .ask-me-bubble.hidden { opacity: 0 !important; pointer-events: none !important; visibility: hidden; }
            .ask-me-bubble:not(.hidden) { opacity: 1 !important; visibility: visible; }

            /* Debug: Make sure bubble is visible by default */
            .ask-me-bubble { display: block !important; }
        </style>
        
	</head>
	<body class="is-preload">

		<!-- Page Wrapper -->
			<div id="page-wrapper">

				<!-- Wrapper -->
					<div id="wrapper">

						<!-- Panel (Banner) -->
							<section class="panel banner right">
								<div class="content color0 span-3-75">
									<h2 class="major">AI Infoways<br /></h2><h3>Empowering Innovation with AI-Intelligence</h3>
									<p align="justify">At AII, we transform ideas into intelligent solutions. As a forward-thinking AI Center of Excellence company, we specialize in AI & ML Intelligence, AI-Powered Chatbots, AI-Powered Android and iOS Apps, future-ready applications using Artificial Intelligence, Machine Learning, and advanced digital technologies.</p>
									<p align="justify">Whether you’re a startup seeking scalable software, an enterprise exploring AI integration, or a learner eager to innovate — AII is your trusted technology partner.</p>
									<p align="justify">We don’t just build intelligence. We build exceptionally, extraordinary possiblities.</p>
									<p align="justify">AI Center of Excellence is your partner in building ethical, scalable, and industry-grade AI.</p>
									<!--<ul class="actions">
										<li><a href="#first" class="button primary color1 circle icon solid fa-angle-right">Next</a></li>
									</ul>-->
								</div>
								<div class="image filtered span-1-75" data-position="25% 25%">
									<!--<img src="images/pic1001.jpg" alt="" />-->
									<script>
										src_temp = "images/" + picked
									 </script>
									<img id="randomBanner" src=src_temp alt="" />
                                   

								</div>
							</section>

						<!-- Newly Placed -->
						<!-- Panel -->
							<section class="panel">
								<!--<div class="intro color2">
									<h2 class="major">Elit integer</h2>
									<p>Sed vel nibh libero. Mauris et lorem pharetra massa lorem turpis congue pulvinar. Vivamus sed feugiat finibus. Duis amet bibendum amet sed. Duis mauris ex, dapibus sed ligula tempus volutpat magna etiam.</p>
								</div>
								-->
								<!-- <div class="gallery">
									<div class="group span-3">
										<a href="images/gallery/fulls/3001.jpg" class="image filtered span-3" data-position="bottom"><img class="randomGallery" src="images/gallery/thumbs/2001.jpg" alt="" /></a>
										<a href="images/gallery/fulls/3002.jpg" class="image filtered span-1-5" data-position="center"><img class="randomGallery" src="images/gallery/thumbs/2002.jpg" alt="" /></a>
										<a href="images/gallery/fulls/3003.jpg" class="image filtered span-1-5" data-position="bottom"><img class="randomGallery" src="images/gallery/thumbs/2003.jpg" alt="" /></a>
									</div>
									<a href="images/gallery/fulls/3004.jpg" class="image filtered span-2-5" data-position="top"><img class="randomGallery" src="images/gallery/thumbs/2004.jpg" alt="" /></a>
								</div> -->
								<!-- <div class="gallery">
									<div class="group span-3">
									  <a href="#" class="image filtered span-3 galleryLink" data-id="2001" data-position="bottom">
										<img class="randomGallery2001" src="images/gallery/thumbs/2001.jpg" alt="" />
									  </a>
									  <a href="#" class="image filtered span-1-5 galleryLink" data-id="2002" data-position="center">
										<img class="randomGallery2002" src="images/gallery/thumbs/2002.jpg" alt="" />
									  </a>
									  <a href="#" class="image filtered span-1-5 galleryLink" data-id="2003" data-position="bottom">
										<img class="randomGallery2003" src="images/gallery/thumbs/2003.jpg" alt="" />
									  </a>
									</div>
									<a href="#" class="image filtered span-2-5 galleryLink" data-id="2004" data-position="top">
									  <img class="randomGallery2004" src="images/gallery/thumbs/2004.jpg" alt="" />
									</a>
								  </div> -->
								  <div class="gallery">
									<div class="group span-3">
									  <a href="#" class="image filtered span-3" data-position="bottom">
										<img class="randomGallery2001" src="images/gallery/thumbs/2001.jpg" alt="" />
									  </a>
									  <a href="#" class="image filtered span-1-5" data-position="center">
										<img class="randomGallery2002" src="images/gallery/thumbs/2002.jpg" alt="" />
									  </a>
									  <a href="#" class="image filtered span-1-5" data-position="bottom">
										<img class="randomGallery2003" src="images/gallery/thumbs/2003.jpg" alt="" />
									  </a>
									</div>
									<a href="#" class="image filtered span-2-5" data-position="top">
									  <img class="randomGallery2004" src="images/gallery/thumbs/2004.jpg" alt="" />
									</a>
								  </div>
								  
								  
								  
							</section>

						<!-- Newly Placed -->
						
						<!-- Panel (Spotlight) -->
							<section class="panel spotlight medium right" id="first">
								<div class="content span-7">
									<h2 class="major">About Us</h2>
									<h3>Who We Are</h3>
									<p align="justify">AII is a next-generation technology company dedicated to pushing boundaries in AI-driven innovation. Founded by visionaries and powered by engineers, we create intelligent solutions for real-world challenges.</p>
									<p align="justify">From AI-Powered intelligence to machine learning applications, our team delivers excellence across minute information. We're passionate about leveraging data, intelligence, and creativity to solve complex problems.</p>
									<p align="justify">At AII, we don’t just deliver projects — we build long-term partnerships. Our cross-functional teams work as an extension of your own, co-creating tech that drives business impact.</p>
								</div>
								
								<div class="image filtered tinted" data-position="top left">
									<!--<img src="images/pic1002.jpg" alt="" />-->
									<!--<img id="randomBanner" src="images/pic1002.jpg" alt="Random Banner Image" />-->
                                    <script>
										src_temp = "images/" + picked1
									 </script>
									<img id="randomBanner1" src=src_temp alt="" />
                                   

								</div>

							</section>
                        
                        <!-- Panel (Spotlight) -->
							<section class="panel spotlight large left">
								<div class="content span-5">
									<h2 class="major">What We Do</h2>
									    <ul>
											<li>AI & ML Algo-Driven Data Intelligence</li>
											<li>Chatbots and Conversational Platforms</li>
											<li>AI-Powered Mobile & Web Apps</li>
											<li>Data Science & Engineering and Cloud Integration</li>
										</ul>
									<h2>Our Vision</h2>
									<p align="justify">To be a catalyst in shaping the intelligent future — one project, one innovation, one solution at a time.</p>
									<h2>Our Mission</h2>
									<p align="justify">To empower businesses and individuals by delivering reliable, scalable, and intelligent AI that transforms how we live and work.</p>
								</div>
								<div class="image filtered tinted" data-position="top right">
									<!--<img src="images/pic1003.jpg" alt="" />-->
									<script>
										src_temp = "images/" + picked2
									 </script>
									<img id="randomBanner2" src=src_temp alt="" />
                                   
								</div>
							</section>

							<section class="panel banner right">
								<div class="content color0 span-3-75">
								  <h2 class="major">AII: Intelligent Drone Technology</h2>
								  <p align="justify">
									AII builds smart, AI-powered drones that serve agriculture, media, logistics, and security — engineered to think, adapt, and perform.
								  </p>
							  
								  <p align="justify">
									<strong>Agriculture Drones:</strong> Monitor crops, analyze soil, and spray with precision.
								  </p>
							  
								  <p align="justify">
									<strong>Photography Drones:</strong> Capture smooth, cinematic shots from the sky.
								  </p>
							  
								  <p align="justify">
									<strong>Mapping Drones:</strong> Create accurate maps for land and site planning.
								  </p>
							  
								  <p align="justify">
									<strong>Firefighting Drones:</strong> Detect hotspots and assist in rescue operations.
								  </p>
							  
								  <p align="justify">
									<strong>Logistics Drones:</strong> Deliver goods quickly with smart route planning.
								  </p>
							  
								  <p align="justify">
									<strong>Security Drones:</strong> Patrol areas and detect threats in real time.
								  </p>
								</div>
							  </section>
							  
							  
							  <section class="panel">
								<div class="gallery">
								  <div class="group span-3">
									<a href="#" class="image filtered span-3" data-position="bottom">
									  <img class="randomGallery" data-id="3013" src="images/gallery/thumbs/a3013.jpg" alt="" />
									</a>
									<a href="#" class="image filtered span-1-5" data-position="center">
									  <img class="randomGallery" data-id="3014" src="images/gallery/thumbs/a3014.jpg" alt="" />
									</a>
									<a href="#" class="image filtered span-1-5" data-position="bottom">
									  <img class="randomGallery" data-id="3015" src="images/gallery/thumbs/a3015.jpg" alt="" />
									</a>
								  </div>
								  <a href="#" class="image filtered span-2-5" data-position="top">
									<img class="randomGallery" data-id="3016" src="images/gallery/thumbs/a3016.jpg" alt="" />
								  </a>
								</div>
							  </section>
							  
							  
						<!-- Panel -->
							<section class="panel color1">
								<div class="intro joined">
									<h2 class="major">Our Services</h2>
									<p align="justify">A 360° Tech Partner for Your Digital Transformation At AII, we believe digital transformation isn’t a one-size-fits-all approach — it’s a strategic evolution. Our service ecosystem is designed to empower startups, enterprises, and governments with end-to-end digital innovation that is intelligent, scalable, and future-ready.</p>
									<p align="justify">Whether building, optimizing, or innovating, All is your partner at every stage of your digital journey.</p>
								</div>
								<div class="intro joined">
									<h3 class="major">What Makes Our Services Different?</h3>
									<ul>
											<li>End-to-End Execution — From concept to deployment</li>
											<li>AI-First Thinking — Every service is built with intelligence at its core</li>
											<li>Agile, Scalable, Reliable — Flexible models that grow with you</li>
											<li>Accelerated Time to Market — Deliver faster, smarter, better</li>
									</ul>
									<p align="justify">Whether you're exploring a new idea or transforming a legacy system, we’re here to make it real.</p>
								</div>
								<div class="inner">
								        
									<!--<img class="randomGallery"src="images/gallery/thumbs/2005.png" alt="" />-->
									<!--<img class="randomBanner3" src="images/gallery/thumbs/"  + picked3 alt="" />-->

									<!-- <script>
									src_temp = "images/gallery/thumbs/" + picked3
									 </script>
									<img id="randomBanner3" src=src_temp alt="" /> -->
										<img class="randomBanner3" src="images/gallery/thumbs/placeholder.png" alt="Random Gallery Image" />

								    	<!--<ul class="grid-icons three connected">
										<li><span class="icon fa-gem"><span class="label">Lorem</span></span></li>
										<li><span class="icon solid fa-camera-retro"><span class="label">Ipsum</span></span></li>
										<li><span class="icon solid fa-cog"><span class="label">Dolor</span></span></li>
										<li><span class="icon solid fa-paper-plane"><span class="label">Sit</span></span></li>
										<li><span class="icon solid fa-chart-bar"><span class="label">Amet</span></span></li>
										<li><span class="icon solid fa-code"><span class="label">Nullam</span></span></li>
									</ul>-->
								</div>
							</section>

						<!-- Panel (Spotlight) -->
							<section class="panel spotlight large left">
								<div class="content span-5">
									<h2 class="major">AI & ML Intelligence</h2>
									<h3>Powering the Future with Algo-Driven Data Intelligence </h3>
									<p align="justify">At AII, Artificial Intelligence and Machine Learning are not auxiliary technologies — they are the very heart of our innovation philosophy and the backbone of every solution we deliver. We envision a world where intelligence is embedded into every digital interaction, and we help businesses bring that vision to life by infusing AI into their processes, products, and decisions. Our AI & ML solutions are designed not just to automate tasks, but to empower organizations to learn from their data, anticipate future trends, and make proactive, strategic decisions. Whether you're navigating the complexities of customer behavior, operational risk, logistics optimization, or content generation, our tailored models adapt to your unique context and grow smarter over time.</p>
								</div>
								<!--<img class="randomGallery" src="images/gallery/thumbs/2011.png" alt="" />-->
								<!-- <script>
									src_temp = "images/" + picked4
								 </script>
								<img id="randomBanner4" src=src_temp alt="" /> -->
								<img id="randomBanner4" src="images/gallery/thumbs/placeholder.png" alt="Random Gallery Image" />

								<div class="image filtered tinted" data-position="top right">
									<img src="images/pic03.jpg" alt="" />
								</div>
							</section>
							<section class="panel spotlight large left">
								<div class="content span-5">
									<h2 class="major">AI-Powered ChatBots</h2>
									<h3>Powering the web apps with AI driven Chatbots</h3>
									<p align="justify">Code. Design. Experience. Impact. At AII, we don’t just build websites. We engineer digital experiences that are responsive, scalable, lightning-fast, and tailored to the evolving demands of your audience. Whether you're a startup looking for your first online presence or an enterprise rebuilding a complex platform, our web development experts craft bespoke web solutions that drive engagement, improve conversions, and scale seamlessly.</p>
									    <ul>
											<li>Design-Led, Code-Driven Approach</li>
											<li>Mobile-First, Future-Proof Websites</li>
											<li>SEO-Ready from Day One</li>
											<li>Cross-Browser, Cross-Device Compatibility</li>
										</ul>
								</div>
								<!--<img class="randomGallery" src="images/gallery/thumbs/2012.png" alt="" />-->
								<img id="randomBanner5" src="images/gallery/thumbs/placeholder.png" alt="Random Image 2012 Variant" />

								<div class="image filtered tinted" data-position="top right">
									<img src="images/pic03.jpg" alt="" />
								</div>
							</section>
							<section class="panel banner right">
								<div class="content color0 span-3-75">
									
									<h2 class="major">AI-Powered Digital Bots</h2>
									<p align="justify">
										At AII, we create intelligent bots that think, adapt, and evolve. These AI agents streamline digital operations and deliver smart automation.
									</p>
							
									<h4>Titan Supreme – G-667C (OpenAI-Trained)</h4>
									<p align="justify">
										A high-performance AI bot built on OpenAI models for advanced automation. Ideal for decision-making, workflow optimization, and data-driven tasks.
									</p>
							
									<h4>Optimus Centauri – 733B (Cognitive Strategist)</h4>
									<p align="justify">
										Optimus Centauri – 733B is a cognitive AI strategist engineered for scenario simulation, market forecasting, and high-stakes decision-making through advanced insight generation and strategic analysis.
									</p>
							
									<h4>Proxima Prime – B-452A (Integration Specialist)</h4>
									<p align="justify">
										An AI bot built to integrate APIs, sync platforms, and optimize workflows. Ensures smooth digital transformation across ecosystems.
									</p>
								</div>
							</section>
							
							<!-- <section class="panel">
								<div class="gallery">
								  <div class="group span-3">
									<a href="#" class="image filtered span-3" data-position="bottom">
									  <img class="randomGallery" data-id="2013" src="images/gallery/thumbs/a2013.jpg" alt="" />
									</a>
									<a href="#" class="image filtered span-1-5" data-position="center">
									  <img class="randomGallery" data-id="2014" src="images/gallery/thumbs/a2014.jpg" alt="" />
									</a>
									<a href="#" class="image filtered span-1-5" data-position="bottom">
									  <img class="randomGallery" data-id="2015" src="images/gallery/thumbs/a2015.jpg" alt="" />
									</a>
								  </div>
								  <a href="#" class="image filtered span-2-5" data-position="top">
									<img class="randomGallery" data-id="2016" src="images/gallery/thumbs/a2016.jpg" alt="" />
								  </a>
								</div>
							  </section> -->
							  <section class="panel">
								<div class="gallery">
								  <div class="group span-3">
									<a href="#" class="image filtered span-3" data-position="bottom">
									  <img class="randomGallery" data-id="2013" src="images/gallery/thumbs/a2013.jpg" alt="" />
									</a>
									<a href="#" class="image filtered span-1-5" data-position="center">
									  <img class="randomGallery" data-id="2014" src="images/gallery/thumbs/a2014.jpg" alt="" />
									</a>
									<a href="#" class="image filtered span-1-5" data-position="bottom">
									  <img class="randomGallery" data-id="2015" src="images/gallery/thumbs/a2015.jpg" alt="" />
									</a>
								  </div>
								  <a href="#" class="image filtered span-2-5" data-position="top">
									<img class="randomGallery" data-id="2016" src="images/gallery/thumbs/a2016.jpg" alt="" />
								  </a>
								</div>
							  </section>
							  
							  
							
							<!-- <section class="panel">
								<div class="gallery">
									<div class="group span-3">
										<a href="#" class="image filtered span-3" data-position="bottom">
											<img class="randomGallery2001" src="images/gallery/thumbs/va2001.jpg" alt="" />
										</a>
										<a href="#" class="image filtered span-1-5" data-position="center">
											<img class="randomGallery2002" src="images/gallery/thumbs/va2002.jpg" alt="" />
										</a>
										<a href="#" class="image filtered span-1-5" data-position="bottom">
											<img class="randomGallery2003" src="images/gallery/thumbs/va2003.jpg" alt="" />
										</a>
									</div>
									<a href="#" class="image filtered span-2-5" data-position="top">
										<img class="randomGallery2004" src="images/gallery/thumbs/va2004.jpg" alt="" />
									</a>
								</div> -->
							</section>
							
						<!-- Panel -->
							<section class="panel color4-alt">
								<div class="inner columns divided">
									<div class="span-3-25">
										<h2 class="major">AI-Powered Mobile Apps</h2>
										<h3>Powered Android & iOS apps with AI Intelligence</h3>
										<p align="justify">At AII, we don’t just develop mobile apps — we craft intelligent digital companions that live in your users' hands and think with your brand’s brain. By the power of Artificial Intelligence with state-of-the-art Android and iOS development, we create mobile experiences that are predictive evolving. A context-aware recommendation engine, or a computer vision-enabled feature that can see and react in real-time.</p>
										<p align="justify">This fusion of AI at the core with mobile at the edge empowers our clients to move beyond simple apps and into ecosystems of intelligence. From e-commerce and finance to healthcare and logistics, our solutions drive engagement, retention, and revenue by creating experiences users don’t just use. If you're looking to redefine what your Android or iOS app can do, partner with AII and unlock the full potential of artificial intelligence.</p>
									</div>
									<div class="span-1-5">
										<h4>What Sets Our AI-Powered Web Apps Apart?</h4>
										<ul>
											<li>User-First Mindset: Everything begins with understanding your users — their needs, behavior, and journey.</li>
											<li>Design that Delivers: Our UX/UI teams blend aesthetics and usability for seamless digital storytelling.</li>
											<li>Modern Tech Stack: Built with cutting-edge frameworks, tools, and cloud platforms.</li>
											<li>Security & Performance at Core: Built for speed, optimized for security, and ready for the future.</li>
									    </ul>
									</div>
								</div>
							</section>
							
						<!-- Panel -->
							<section class="panel">
								<div class="intro color2">
									<h3 class="major">AI driven eCom Apps</h3>
									<p>Build secure, high-converting online stores with:</p>
									<ul>
											<li>Shopify, Magento, WooCommerce, BigCommerce</li>
											<li>Custom e-commerce platforms with payment gateways</li>
											<li>Inventory management, shipping integrations, analytics dashboards</li>
											<li>Mobile-ready cart and checkout flows</li>
									</ul>
									<p>"We're not just developers — we're your AI transformation architects."</p>
								</div>
								<div>
									<!--<img src="images/pic1004.jpg" alt="" width="260" height="470" />-->
									<img id="randomBanner6" src="images/placeholder.jpg" alt="Random Image 1004 Variant"
     width="260" height="485" style="object-fit: cover;" />


								</div>
								<div class="intro color2">
								    <h3 class="major">AI driven Bot Training</h3>
									<p align="justify">What sets our apps apart is not just their functionality but their intuition. Built using native and cross-platform technologies like Kotlin, Swift, Flutter, and React Native, our Android and iOS apps are supercharged with machine learning models, NLP engines, and AI APIs that adapt to users in real time. Our robust AI infrastructure — trained, deployed, and continuously optimized in the cloud — ensuring that every user experience is lightning-fast, contextually relevant, and stunningly intuitive.</p>
									<!--<p align="justify">Reach out now — b'coz intelligence starts with a conversation.</p>-->
								</div>
								<div class="gallery">
									<!--<div class="group span-3">
										<a href="images/gallery/fulls/01.jpg" class="image filtered span-3" data-position="bottom"><img src="images/gallery/thumbs/01.jpg" alt="" /></a>
										<a href="images/gallery/fulls/02.jpg" class="image filtered span-1-5" data-position="center"><img src="images/gallery/thumbs/02.jpg" alt="" /></a>
										<a href="images/gallery/fulls/03.jpg" class="image filtered span-1-5" data-position="bottom"><img src="images/gallery/thumbs/03.jpg" alt="" /></a>
									</div>
									-->
									<!--<a href="images/gallery/fulls/04.jpg" class="image filtered span-2-5" data-position="top"><img src="images/gallery/thumbs/04.jpg" alt="" /></a>-->
									<!-- <div class="group span-4-5">
										<a href="images/gallery/fulls/3005.jpg" class="image filtered span-3" data-position="top"><img class="randomGallery"src="images/gallery/thumbs/2005.jpg" alt="" /></a>
										<a href="images/gallery/fulls/3006.jpg" class="image filtered span-1-5" data-position="center"><img class="randomGallery"src="images/gallery/thumbs/2006.jpg" alt="" /></a>
										<a href="images/gallery/fulls/3007.jpg" class="image filtered span-1-5" data-position="bottom"><img class="randomGallery" src="images/gallery/thumbs/2007.jpg" alt="" /></a>
										<a href="images/gallery/fulls/3008.jpg" class="image filtered span-3" data-position="top"><img class="randomGallery" src="images/gallery/thumbs/2008.jpg" alt="" /></a>
									</div> -->
									<!--<a href="images/gallery/fulls/09.jpg" class="image filtered span-2-5" data-position="right"><img src="images/gallery/thumbs/09.jpg" alt="" /></a>-->
									 <!-- <div class="group span-4-5">
										<a href="#" class="image filtered span-3 galleryLink" data-id="2005">
										  <img class="randomGallery2005" src="images/gallery/thumbs/2005.jpg" alt="" />
										</a>
										<a href="#" class="image filtered span-1-5 galleryLink" data-id="2006">
										  <img class="randomGallery2006" src="images/gallery/thumbs/2006.jpg" alt="" />
										</a>
										<a href="#" class="image filtered span-1-5 galleryLink" data-id="2007">
										  <img class="randomGallery2007" src="images/gallery/thumbs/2007.jpg" alt="" />
										</a>
										<a href="#" class="image filtered span-3 galleryLink" data-id="2008">
										  <img class="randomGallery2008" src="images/gallery/thumbs/2008.jpg" alt="" />
										</a>
									  </div> -->
									  <div class="group span-4-5">
										<a href="#" class="image filtered span-3" data-position="top">
										  <img class="randomGallery2005" src="images/gallery/thumbs/2005.jpg" alt="" />
										</a>
										<a href="#" class="image filtered span-1-5" data-position="center">
										  <img class="randomGallery2006" src="images/gallery/thumbs/2006.jpg" alt="" />
										</a>
										<a href="#" class="image filtered span-1-5" data-position="bottom">
										  <img class="randomGallery2007" src="images/gallery/thumbs/2007.jpg" alt="" />
										</a>
										<a href="#" class="image filtered span-3" data-position="top">
										  <img class="randomGallery2008" src="images/gallery/thumbs/2008.jpg" alt="" />
										</a>
									  </div>
										 
									   
							</section>

						<!-- Panel -->
							<section class="panel color4-alt">
								<div class="intro color4">
									<h2 class="major">Contact Us</h2>
									<p align="justify">At AI Infoways, every conversation is the start of something intelligent — whether it’s crafting AI-powered Android and iOS apps, building intuitive chatbots that speak your brand’s language, or deploying machine learning models that think ahead of your business needs. As pioneers in Artificial Intelligence and next-gen digital solutions, we partner with visionaries to bring bold ideas to life through real-time insights, automation, and smarter mobile experiences. Ready to co-create a future led by AI & ML innovation?</p>
								</div>
								<div class="inner columns divided">
									<div class="span-3-25">
										<form method="post" action="#">
											<div class="fields">
												<div class="field half">
													<label for="name">Name</label>
													<input type="text" name="name" id="name" />
												</div>
												<div class="field half">
													<label for="email">Email</label>
													<input type="email" name="email" id="email" />
												</div>
												<div class="field">
													<label for="message">Message</label>
													<textarea name="message" id="message" rows="4"></textarea>
												</div>
											</div>
											<ul class="actions">
												<li><input type="submit" value="Send Message" class="button primary" /></li>
											</ul>
										</form>
									</div>
									<div class="span-1-5">
										<ul class="contact-icons color1">
											<li class="icon brands fa-twitter"><a href="#"><EMAIL></a></li>
											<li class="icon brands fa-facebook-f"><a href="#">+1-504-444-2678</a></li>
											<li class="icon brands fa-snapchat-ghost"><a href="#">www.aiinfoways.com</a></li>
											<!--<li class="icon brands fa-instagram"><a href="#">@untitled-tld</a></li>
											<li class="icon brands fa-medium-m"><a href="#">medium.com/untitled</a></li>-->
										</ul>
									</div>
								</div>
							</section>

						<!-- Panel -->
<!--							<section class="panel color2-alt">
								<div class="intro color2">
									<h2 class="major">Elements</h2>
									<p>Sed vel nibh libero. Mauris et lorem pharetra massa lorem turpis congue pulvinar. Vivamus sed feugiat finibus. Duis amet bibendum amet sed. Duis mauris ex, dapibus sed ligula tempus volutpat magna etiam. </p>
								</div>
								<div class="inner columns aligned">
									<div class="span-2-75">

										<h3 class="major">Text</h3>
										<p>This is <b>bold</b> and this is <strong>strong</strong>. This is <i>italic</i> and this is <em>emphasized</em>.
										This is <sup>superscript</sup> text and this is <sub>subscript</sub> text.
										This is <u>underlined</u> and this is code: <code>for (;;) { ... }</code>. Finally, <a href="#">this is a link</a>.</p>

										<h1>Heading Level 1</h1>
										<h2>Heading Level 2</h2>
										<h3>Heading Level 3</h3>
										<h4>Heading Level 4</h4>
										<h5>Heading Level 5</h5>
										<h6>Heading Level 6</h6>

									</div>
									<div class="span-3">

										<h4>Blockquote</h4>
										<blockquote>Lorem ipsum dolor sit amet. Felis sagittis eget tempus euismod. Vestibulum ante ipsum primis in vestibulum. Blandit adipiscing eu iaculis volutpat ac adipiscing volutpat ac adipiscing faucibus.</blockquote>

										<h4>Preformatted</h4>
										<pre><code>i = 0;

while (!deck.isInOrder()) {
    print 'Iteration ' + (i++);
    deck.shuffle();
}

print 'It took ' + i + ' iterations to sort the deck.';</code></pre>

									</div>
									<div class="span-1-25">

										<h3 class="major">Lists</h3>

										<h4>Unordered</h4>
										<ul>
											<li>Lorem ipsum dolor sit.</li>
											<li>Dolor pulvinar etiam.</li>
											<li>Etiam vel felis viverra.</li>
										</ul>

										<h4>Alternate</h4>
										<ul class="alt">
											<li>Lorem ipsum dolor sit.</li>
											<li>Dolor pulvinar etiam.</li>
											<li>Etiam vel felis viverra.</li>
											<li>Felis enim feugiat.</li>
										</ul>

									</div>
									<div class="span-1-5">

										<h4>Ordered</h4>
										<ol>
											<li>Lorem ipsum dolor sit.</li>
											<li>Dolor pulvinar etiam.</li>
											<li>Etiam vel felis viverra.</li>
											<li>Felis enim feugiat.</li>
											<li>Etiam vel felis lorem.</li>
										</ol>

										<h4>Actions</h4>
										<ul class="actions">
											<li><a href="#" class="button primary color2">Default</a></li>
											<li><a href="#" class="button">Default</a></li>
										</ul>
										<ul class="actions stacked">
											<li><a href="#" class="button primary color2">Default</a></li>
											<li><a href="#" class="button">Default</a></li>
										</ul>

									</div>
									<div class="span-1-25">

										<h4>Icons</h4>
										<ul class="icons">
											<li><a href="#" class="icon brands fa-twitter"><span class="label">Twitter</span></a></li>
											<li><a href="#" class="icon brands fa-facebook-f"><span class="label">Facebook</span></a></li>
											<li><a href="#" class="icon brands fa-instagram"><span class="label">Instagram</span></a></li>
											<li><a href="#" class="icon brands fa-github"><span class="label">GitHub</span></a></li>
											<li><a href="#" class="icon brands fa-medium-m"><span class="label">Medium</span></a></li>
										</ul>

										<h4>Contact Icons</h4>
										<ul class="contact-icons color2">
											<li class="icon brands fa-twitter"><a href="#">Twitter</a></li>
											<li class="icon brands fa-facebook-f"><a href="#">Facebook</a></li>
											<li class="icon brands fa-instagram"><a href="#">Instagram</a></li>
											<li class="icon brands fa-github"><a href="#">GitHub</a></li>
											<li class="icon brands fa-medium-m"><a href="#">Medium</a></li>
										</ul>

									</div>
									<div class="span-3">
										<h3 class="major">Table</h3>
										<h4>Default</h4>
										<div class="table-wrapper">
											<table>
												<thead>
													<tr>
														<th>Name</th>
														<th>Description</th>
														<th>Price</th>
													</tr>
												</thead>
												<tbody>
													<tr>
														<td>Item One</td>
														<td>Ante turpis integer aliquet porttitor.</td>
														<td>29.99</td>
													</tr>
													<tr>
														<td>Item Two</td>
														<td>Vis ac commodo adipiscing arcu aliquet.</td>
														<td>19.99</td>
													</tr>
													<tr>
														<td>Item Three</td>
														<td> Morbi faucibus arcu accumsan lorem.</td>
														<td>29.99</td>
													</tr>
													<tr>
														<td>Item Four</td>
														<td>Vitae integer tempus condimentum.</td>
														<td>19.99</td>
													</tr>
												</tbody>
												<tfoot>
													<tr>
														<td colspan="2"></td>
														<td>100.00</td>
													</tr>
												</tfoot>
											</table>
										</div>
									</div>
									<div class="span-3">
										<h4>Alternate</h4>
										<div class="table-wrapper">
											<table class="alt">
												<thead>
													<tr>
														<th>Name</th>
														<th>Description</th>
														<th>Price</th>
													</tr>
												</thead>
												<tbody>
													<tr>
														<td>Item One</td>
														<td>Ante turpis integer aliquet porttitor.</td>
														<td>29.99</td>
													</tr>
													<tr>
														<td>Item Two</td>
														<td>Vis ac commodo adipiscing arcu aliquet.</td>
														<td>19.99</td>
													</tr>
													<tr>
														<td>Item Three</td>
														<td> Morbi faucibus arcu accumsan lorem.</td>
														<td>29.99</td>
													</tr>
													<tr>
														<td>Item Four</td>
														<td>Vitae integer tempus condimentum.</td>
														<td>19.99</td>
													</tr>
													<tr>
														<td>Item Five</td>
														<td>Ante turpis integer aliquet porttitor.</td>
														<td>29.99</td>
													</tr>
												</tbody>
												<tfoot>
													<tr>
														<td colspan="2"></td>
														<td>100.00</td>
													</tr>
												</tfoot>
											</table>
										</div>
									</div>
									<div class="span-2-25">
										<h3 class="major">Buttons</h3>
										<ul class="actions">
											<li><a href="#" class="button primary color2">Primary</a></li>
											<li><a href="#" class="button">Default</a></li>
										</ul>
										<ul class="actions">
											<li><a href="#" class="button">Default</a></li>
											<li><a href="#" class="button large">Large</a></li>
											<li><a href="#" class="button small">Small</a></li>
										</ul>
										<ul class="actions">
											<li><a href="#" class="button primary color2 icon solid fa-cog">Icon</a></li>
											<li><a href="#" class="button icon fa-gem">Icon</a></li>
										</ul>
										<ul class="actions">
											<li><span class="button primary color2 disabled">Disabled</span></li>
											<li><span class="button disabled">Disabled</span></li>
										</ul>
										<ul class="actions">
											<li><a href="#" class="button primary color2 circle icon solid fa-cog">Icon</a></li>
											<li><a href="#" class="button circle icon fa-gem">Icon</a></li>
										</ul>
									</div>
									<div class="span-4-5">
										<h3 class="major">Form</h3>
										<form method="post" action="#">
											<div class="fields">
												<div class="field third">
													<label for="demo-name">Name</label>
													<input type="text" name="demo-name" id="demo-name" value="" placeholder="Jane Doe" />
												</div>
												<div class="field third">
													<label for="demo-email">Email</label>
													<input type="email" name="demo-email" id="demo-email" value="" placeholder="<EMAIL>" />
												</div>
												<div class="field third">
													<label for="demo-category">Category</label>
													<div class="select-wrapper">
														<select name="demo-category" id="demo-category">
															<option value="">-</option>
															<option value="1">Manufacturing</option>
															<option value="1">Shipping</option>
															<option value="1">Administration</option>
															<option value="1">Human Resources</option>
														</select>
													</div>
												</div>
												<div class="field quarter">
													<input type="radio" id="demo-priority-low" name="demo-priority" class="color2" checked />
													<label for="demo-priority-low">Low Priority</label>
												</div>
												<div class="field quarter">
													<input type="radio" id="demo-priority-high" name="demo-priority" class="color2" />
													<label for="demo-priority-high">High Priority</label>
												</div>
												<div class="field quarter">
													<input type="checkbox" id="demo-copy" name="demo-copy" class="color2" />
													<label for="demo-copy">Email a copy</label>
												</div>
												<div class="field quarter">
													<input type="checkbox" id="demo-human" name="demo-human" class="color2" checked />
													<label for="demo-human">Not a robot</label>
												</div>
												<div class="field">
													<label for="demo-message">Message</label>
													<textarea name="demo-message" id="demo-message" placeholder="Enter your message" rows="2"></textarea>
												</div>
											</div>
											<ul class="actions">
												<li><input type="submit" value="Send Message" class="primary color2" /></li>
												<li><input type="reset" value="Reset" /></li>
											</ul>
										</form>
									</div>
								</div>
							</section>
-->
						<!-- Copyright -->
							<div class="copyright">&copy; Untitled. Design: <a href="https://html5up.net">HTML5 UP</a>.</div>

					</div>

			</div>

		<!-- Scripts -->
			<script src="assets/js/jquery.min.js"></script>
			<script src="assets/js/browser.min.js"></script>
			<script src="assets/js/breakpoints.min.js"></script>
			<script src="assets/js/main.js"></script>
			<!-- CHATBOT WIDGET HTML (Close button removed from header) -->
    <!-- CHATBOT WIDGET HTML -->
    <div id="chat-widget-container">
        <div id="chat-popup">
            <div class="chat-header">
                <h3>AI Assistant</h3>
                <button id="chat-close-btn">×</button>
            </div>
            <div class="chat-box" id="chat-box-messages">
                <!-- Messages will appear here -->
            </div>
            <div class="chat-footer">
                <input type="text" id="chat-input" placeholder="Ask a question..." autocomplete="off">
                <button id="chat-send-btn">Send</button>
            </div>
        </div>

        <!-- Ask Me Speech Bubble -->
        <div class="ask-me-bubble">Ask me</div>

        <div id="chat-bubble">
            <!-- AI Assistant Robot Icon -->
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <!-- Main Robot Head -->
                <rect x="6" y="8" width="24" height="20" rx="4" fill="white" stroke="none"/>

                <!-- Top Panel -->
                <rect x="8" y="10" width="20" height="3" rx="1.5" fill="#e5e7eb"/>

                <!-- Eyes -->
                <circle cx="13" cy="18" r="2.5" fill="#2563eb"/>
                <circle cx="23" cy="18" r="2.5" fill="#2563eb"/>

                <!-- Eye highlights -->
                <circle cx="13.5" cy="17.5" r="1" fill="white" opacity="0.9"/>
                <circle cx="23.5" cy="17.5" r="1" fill="white" opacity="0.9"/>

                <!-- Mouth/Speaker grille -->
                <rect x="15" y="22" width="6" height="1" rx="0.5" fill="#6b7280"/>
                <rect x="16" y="24" width="4" height="1" rx="0.5" fill="#6b7280"/>

                <!-- Side panels -->
                <rect x="4" y="14" width="2" height="8" rx="1" fill="white"/>
                <rect x="30" y="14" width="2" height="8" rx="1" fill="white"/>

                <!-- Antenna -->
                <line x1="18" y1="8" x2="18" y2="4" stroke="white" stroke-width="2" stroke-linecap="round"/>
                <circle cx="18" cy="3" r="2" fill="white"/>
                <circle cx="18" cy="3" r="1" fill="#2563eb"/>

                <!-- AI Badge -->
                <rect x="12" y="30" width="12" height="4" rx="2" fill="white" opacity="0.9"/>
                <text x="18" y="33" text-anchor="middle" fill="#2563eb" font-size="7" font-weight="bold" font-family="Arial, sans-serif">AI</text>
            </svg>
        </div>
    </div>

    <!-- UPDATED CHATBOT JAVASCRIPT -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get all the HTML elements for the chatbot
            const chatPopup = document.getElementById('chat-popup');
            const chatBubble = document.getElementById('chat-bubble');
            const chatCloseBtn = document.getElementById('chat-close-btn');
            const chatInput = document.getElementById('chat-input');
            const chatSendBtn = document.getElementById('chat-send-btn');
            const chatBox = document.getElementById('chat-box-messages');
            const askMeBubble = document.querySelector('.ask-me-bubble');
            const backendUrl = 'chatbot_backend_ai.php';

            // Debug: Check if askMeBubble element is found
            console.log('askMeBubble element found:', askMeBubble);

            // Ensure askMe bubble is visible on page load
            if (askMeBubble) {
                askMeBubble.classList.remove('hidden');
                askMeBubble.style.display = 'block';
                console.log('askMeBubble made visible on page load');
            }

            // --- Event Listeners ---
            // Toggle the chat window when the bubble is clicked
            chatBubble.addEventListener('click', function(event) {
                event.stopPropagation(); // Prevents the 'click outside' listener from firing immediately

                // Check current state before toggling
                const isCurrentlyOpen = chatPopup.classList.contains('open');

                chatPopup.classList.toggle('open');

                // Hide/show "Ask me" bubble based on NEW chat state
                if (!isCurrentlyOpen) {
                    // Chat is opening, hide askMe bubble
                    if (askMeBubble) {
                        askMeBubble.classList.add('hidden');
                    }
                    console.log('Chat opened, hiding askMeBubble');
                } else {
                    // Chat is closing, show askMe bubble
                    if (askMeBubble) {
                        askMeBubble.classList.remove('hidden');
                    }
                    console.log('Chat closed, showing askMeBubble');
                }
            });

            // Close the chat window when the close button is clicked
            chatCloseBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                chatPopup.classList.remove('open');

                // Show "Ask me" bubble when chat closes
                setTimeout(function() {
                    if (askMeBubble) {
                        askMeBubble.classList.remove('hidden');
                        console.log('Close button: askMeBubble should now be visible');
                    }
                }, 100); // Small delay to ensure chat is closed first

                console.log('Close button clicked, askMeBubble element:', askMeBubble);
            });

            // Send a message
            chatSendBtn.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', function(e) { 
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // --- NEW: CLICK OUTSIDE TO CLOSE ---
            // Listen for a click anywhere on the page
            document.addEventListener('click', function(event) {
                // Check if the chat is open AND the click was NOT on the chat bubble or inside the chat popup
                if (chatPopup.classList.contains('open') && !chatBubble.contains(event.target) && !chatPopup.contains(event.target)) {
                    chatPopup.classList.remove('open');
                }
            });


            // --- Main Functions ---
            function appendMessage(message, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `chat-message ${sender}-message`; 
                messageDiv.innerHTML = message;
                chatBox.appendChild(messageDiv);
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            async function sendMessage() {
                const message = chatInput.value.trim();
                if (!message) return;
                
                appendMessage(message, 'user');
                chatInput.value = '';
                
                try {
                    const response = await fetch(backendUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ question: message })
                    });
                    const data = await response.json();
                    appendMessage(data.answer, 'bot');
                } catch (error) {
                    console.error('Chatbot error:', error);
                    appendMessage('Connection failed. Please check the backend.', 'bot');
                }
            }

            // Get the initial greeting message when the page loads
            async function getGreeting() {
                 try {
                    const response = await fetch(backendUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ question: 'hello' })
                    });
                    const data = await response.json();
                    appendMessage(data.answer, 'bot');
                } catch (error) {
                    appendMessage('Welcome! How can I help you?', 'bot');
                }
            }
            
            // Call for the greeting
            getGreeting();
        });
    </script>
           <!-- <script>
				// Banner images list
				const bannerImages = [
				  "images/pic1001.jpg",
				  "images/pic1002.jpg",
				  "images/pic1003.jpg"
				];
			  
				// On load: pick a random banner image
				window.addEventListener("DOMContentLoaded", () => {
				  const banner = document.getElementById("randomBanner");
				  const randomBanner = bannerImages[Math.floor(Math.random() * bannerImages.length)];
				  banner.src = randomBanner;
			  
				  // Randomize gallery images
				  const galleryImgs = document.querySelectorAll(".randomGallery");
				  const gallerySources = [
					"2001.jpg", "2002.jpg", "2003.jpg", "2004.jpg", "2005.jpg", "2006.jpg", "2007.jpg", "2008.jpg"
				  ];
			  
				  galleryImgs.forEach(img => {
					const random = gallerySources[Math.floor(Math.random() * gallerySources.length)];
					img.src = "images/gallery/thumbs/" + random;
				  });
				});
			  </script>-->
			  <!--<script>
				window.addEventListener("DOMContentLoaded", () => {
				  // ✅ Banner image logic
				  const bannerImages = [
					"images/pic1001.jpg",
					"images/pic1002.jpg",
					"images/pic1003.jpg"
				  ];
				  const banner = document.getElementById("randomBanner");
				  if (banner) {
					const chosen = bannerImages[Math.floor(Math.random() * bannerImages.length)];
					banner.src = chosen;
				  }
			  
				  // ✅ Gallery logic
				  const galleryImgs = document.querySelectorAll(".randomGallery");
				  const gallerySources = [
					"2001.jpg", "2002.jpg", "2003.jpg", "2004.jpg", "2005.jpg", "2006.jpg", "2007.jpg", "2008.jpg"
				  ];
			  
				  galleryImgs.forEach(img => {
					const random = gallerySources[Math.floor(Math.random() * gallerySources.length)];
					const newSrc = "images/gallery/thumbs/" + random;
			  
					// Create test image to verify it exists
					const testImg = new Image();
					testImg.onload = () => img.src = newSrc;
					testImg.onerror = () => console.warn("Missing image:", newSrc);
					testImg.src = newSrc;
				  });
				});
			  </script>-->
			  <!--<script>
				window.addEventListener("DOMContentLoaded", () => {
				  // ✅ Banner image randomizer
				  const bannerImages = [
					"images/pic1001.jpg",
					"images/pic1002.jpg",
					"images/pic1003.jpg"
				  ];
				  const banner = document.getElementById("randomBanner");
				  if (banner) {
					const selected = bannerImages[Math.floor(Math.random() * bannerImages.length)];
					banner.src = selected;
				  }
				
				  // ✅ Gallery image randomizer
				  const galleryImgs = document.querySelectorAll(".randomGallery");
				  const gallerySources = [
					"2001.jpg", "2002.jpg", "2003.jpg", "2004.jpg",
					"2005.jpg", "2006.jpg", "2007.jpg", "2008.jpg"
				  ];
				
				  galleryImgs.forEach(img => {
					const picked = gallerySources[Math.floor(Math.random() * gallerySources.length)];
					const newSrc = "images/gallery/thumbs/" + picked;
				
					const preload = new Image();
					preload.onload = () => {
					  img.src = newSrc;
					};
					preload.onerror = () => {
					  console.warn("Image failed to load:", newSrc);
					};
					preload.src = newSrc;
				  });
				});
				</script>-->
				<!--<script>
					window.addEventListener("DOMContentLoaded", () => {
					  // ✅ Random Banner Image
					  const bannerImages = [
						"images/pic1001.jpg",
						"images/pic1002.jpg",
						"images/pic1003.jpg"
					  ];
					  const banner = document.getElementById("randomBanner");
					  if (banner) {
						const picked = bannerImages[Math.floor(Math.random() * bannerImages.length)];
						banner.src = picked;
					  }
					
					  const galleryImgs = document.querySelectorAll(".randomGallery");

galleryImgs.forEach(img => {
  const randomImage = gallerySources[Math.floor(Math.random() * gallerySources.length)];
  // Add cache buster with current time so browser loads fresh image
  const newSrc = "images/gallery/thumbs/" + randomImage + "?t=" + new Date().getTime();

  const testImg = new Image();
  testImg.onload = () => img.src = newSrc;
  testImg.onerror = () => {
    console.warn("Missing image:", newSrc);
    img.src = "images/gallery/thumbs/placeholder.jpg";  // fallback image if missing
  };
  testImg.src = newSrc;
});


					
					</script>-->
					
						<!-- Your HTML content here -->
					  
						
						
						<!--<script>
						  window.addEventListener('load', () => {
							// Your random image code here
					  
							const bannerImages = [
							  "images/aix1001.jpg",
							  "images/aix1002.jpg",
							  "images/aix1003.jpg",
							  "images/aix1004.jpg",
							  "images/aix1005.jpg",
							  "images/aix1006.jpg",
							  "images/aix1007.jpg",
							  "images/aix1008.jpg",
							  "images/apic1001.jpg"
                            

							];
							
							const banner = document.getElementById("randomBanner");
							if (banner) {
							  const picked = bannerImages[Math.floor(Math.random() * bannerImages.length)];
							  banner.src = picked + "?t=" + new Date().getTime();  // cache buster
							  //document.write(newSrc + "<br>")
							  //document.write(picked + "<br>")
							}
					  
							// const galleryImgs = document.querySelectorAll(".randomGallery");
							// const gallerySources = [
							//   "2001.jpg", "2002.jpg", "2003.jpg", "2004.jpg",
							//   "2005.jpg", "2006.jpg", "2007.jpg", "2008.jpg"
							//];
					  
							// galleryImgs.forEach(img => {
							//   const random = gallerySources[Math.floor(Math.random() * gallerySources.length)];
							//   const newSrc = "images/gallery/thumbs/" + random + "?t=" + new Date().getTime();
							  

					  
							//   const testImg = new Image();
							//   testImg.onload = () => img.src = newSrc;
							//   testImg.onerror = () => {
							// 	console.warn("Missing image:", newSrc);
							// 	img.src = "images/gallery/thumbs/placeholder.jpg";
							//   };
							//   testImg.src = newSrc;
							// });
						  });
						</script>-->
						<!--<script>
							window.addEventListener('load', () => {
							  // Your random image code here
						
							  const bannerImages1 = [
								// "images/aix1001.jpg",
								// "images/aix1002.jpg",
								// "images/aix1003.jpg",
								// "images/aix1004.jpg",
								// "images/aix1005.jpg",
								// "images/aix1006.jpg",
								// "images/aix1007.jpg",
								// "images/aix1008.jpg",
								// "images/apic1001.jpg",
								"images/pic1002.jpg",
								"images/bpic1002.jpg",
								"images/cpic1002.jpg",
								"images/dpic1002.jpg"
	
	
							  ];
							  
							  const banner = document.getElementById("randomBanner");
							  if (banner) {
								const picked1 = bannerImages1[Math.floor(Math.random() * bannerImages1.length)];
								banner.src = picked1 + "?t=" + new Date().getTime();  // cache buster
								//document.write(newSrc + "<br>")
								//document.write(picked + "<br>")
							  }
							}); 
							</script>-->
							<script>
								window.addEventListener('load', () => {
									
							
								  const bannerImages = [
									
									"images/aix1001.jpg",
									"images/aix1002.jpg",
									"images/aix1003.jpg",
									"images/aix1004.jpg",
									"images/aix1005.jpg"
									
									
								  ];
								  
							  
								  const banners = document.querySelectorAll("#randomBanner");
								  banners.forEach(banner => {
									const picked = bannerImages[Math.floor(Math.random() * bannerImages.length)];
									banner.src = picked + "?t=" + new Date().getTime(); // cache-busting
								});
								const bannerImages1 = [
								  // Banner randomization
								    "images/pic1002.jpg",
									"images/apic1002.jpg",
								    "images/bpic1002.jpg",
								    "images/cpic1002.jpg",
								    "images/dpic1002.jpg"
									
							  ];
							  
							  const banner = document.getElementById("randomBanner1");
							  if (banner) {
								const picked1 = bannerImages1[Math.floor(Math.random() * bannerImages1.length)];
								banner.src = picked1 + "?t=" + new Date().getTime();  // cache buster
								
							  } 
						});
							
						
							
							const bannerImages2 = [
								
	                             "images/pic1003.jpg",
							    "images/apic1003.jpg",
							"images/bpic1003.jpg",
								"images/cpic1003.jpg",
							"images/dpic1003.jpg"
							
						];
							  
							  const banners = document.querySelectorAll("#randomBanner2");
						 		  banners.forEach(banner => {
									const picked2 = bannerImages2[Math.floor(Math.random() * bannerImages2.length)];
								banner.src = picked2 + "?t=" + new Date().getTime(); // cache-busting
								
								
					});
					
								
						
								
							  </script>
							  <!-- <script>
								window.addEventListener('load', () => {
									
								const bannerImages3 = [
								
	                            
								"images/gallery/thumbs/2005.png",
								"images/gallery/thumbs/a2005.png",
								"images/gallery/thumbs/b2005.png",
								"images/gallery/thumbs/c2005.png"
								


							  ];
							  
							  const banners = document.getElementById("randomBanner3");
							  if (banner) {
								const picked3 = bannerImages3[Math.floor(Math.random() * bannerImages1.length)];
								banner.src = picked3 + "?t=" + new Date().getTime();  // cache buster
								
							  } 
									
								
							});		
							  </script>
							 
								
					
					  
			   -->
			   <script>
				window.addEventListener('load', () => {
				  const bannerImages3 = [
					"images/gallery/thumbs/2005.png",
					"images/gallery/thumbs/ai2005.png",
					"images/gallery/thumbs/bi2005.png",
					"images/gallery/thumbs/ci2005.png",
					"images/gallery/thumbs/di2005.png"
				  ];
			  
				  const banners = document.querySelectorAll(".randomBanner3");
			  
				  banners.forEach(banner => {
					const picked3 = bannerImages3[Math.floor(Math.random() * bannerImages3.length)];
					banner.src = picked3 + "?t=" + new Date().getTime(); // cache busting
				  });


			// 	  const bannerImages4 = [
			// 		"images/gallery/thumbs/2011.png",
			//    "images/gallery/thumbs/a2011.png",
			//    "images/gallery/thumbs/b2011.png",
			//    "images/gallery/thumbs/c2011.png"
			// 	  ];
			  
			// 	  const banner = document.querySelectorAll(".randomBanner3");
			  
			// 	  banner.forEach(banner => {
			// 		const picked4 = bannerImages4[Math.floor(Math.random() * bannerImages4.length)];
			// 		banner.src = picked4 + "?t=" + new Date().getTime(); // cache busting
			// 	  });
				});
			  </script>
 			  <script>
				window.addEventListener('load', () => {
				  const bannerImages4 = [
					"images/gallery/thumbs/2011.png",
					"images/gallery/thumbs/a2011.png",
					"images/gallery/thumbs/b2011.png",
					"images/gallery/thumbs/c2011.png",
					"images/gallery/thumbs/d2011.png"
				  ];
			  
				  const banner = document.getElementById("randomBanner4"); // Use ID
			  
				  if (banner) {
					const picked4 = bannerImages4[Math.floor(Math.random() * bannerImages4.length)];
					banner.src = picked4 + "?t=" + new Date().getTime(); // cache-busting
				  }
				});
			  </script>
			<script>
				window.addEventListener('load', () => {
				  const bannerImages5 = [
					"images/gallery/thumbs/2012.png",
					"images/gallery/thumbs/a2012.png",
					"images/gallery/thumbs/b2012.png",
					"images/gallery/thumbs/c2012.png",
					"images/gallery/thumbs/d2012.png"
				  ];
			  
				  const banner = document.getElementById("randomBanner5");
			  
				  if (banner) {
					const picked5 = bannerImages5[Math.floor(Math.random() * bannerImages5.length)];
					banner.src = picked5 + "?t=" + new Date().getTime(); // cache-busting
				  }
				});
			  </script>
			  <script>
				window.addEventListener('load', () => {
				  const bannerImages6 = [
					"images/pic1004.jpg",
					"images/apic1004.jpg",
					"images/bpic1004.jpg",
					"images/cpic1004.jpg",
					"images/dpic1004.jpg"

				
				  ];
			  
				  const banner = document.getElementById("randomBanner6");
			  
				  if (banner) {
					const picked6 = bannerImages6[Math.floor(Math.random() * bannerImages6.length)];
					banner.src = picked6 + "?t=" + new Date().getTime(); // cache-busting
				  }
				});
			  </script>
			  <!-- <script>
				window.addEventListener('load', () => {
				  const variants = {
					"2001": ["2001.jpg", "a2001.jpg", "b2001.jpg", "c2001.jpg"],
					"2002": ["2002.jpg", "a2002.jpg", "b2002.jpg", "c2002.jpg"],
					"2003": ["2003.jpg", "a2003.jpg", "b2003.jpg", "c2003.jpg"],
					"2004": ["2004.jpg", "a2004.jpg", "b2004.jpg", "c2004.jpg"]
				  };
			  
				  Object.keys(variants).forEach(id => {
					const img = document.querySelector(`.randomGallery${id}`);
					if (img) {
					  const options = variants[id];
					  const picked = options[Math.floor(Math.random() * options.length)];
					  img.src = `images/gallery/thumbs/${picked}?t=${new Date().getTime()}`;
					}
				  });
				});
			  </script> -->
			  <script>
				window.addEventListener('load', () => {
				  const variants = {
					"2001": ["2001.jpg", "a2001.jpg", "b2001.jpg", "c2001.jpg","d2001.jpg"],
					"2002": ["2002.jpg", "a2002.jpg", "b2002.jpg", "c2002.jpg","d2002.jpg"],
					"2003": ["2003.jpg", "a2003.jpg", "b2003.jpg", "c2003.jpg","d2003.jpg"],
					"2004": ["2004.jpg", "a2004.jpg", "b2004.jpg", "c2004.jpg","d2004.jpg"]
				  };
			  
				  Object.keys(variants).forEach(id => {
					const img = document.querySelector(`.randomGallery${id}`);
					if (img) {
					  const options = variants[id];
					  const picked = options[Math.floor(Math.random() * options.length)];
					  img.src = `images/gallery/thumbs/${picked}?t=${new Date().getTime()}`;
					}
				  });
				});
			  </script>
			 <!-- <script>
				
  window.addEventListener('load', () => {
    const variants = {
      "2013": ["a2013.jpg", "b2013.jpg", "c2013.jpg", "d2013.jpg"],
      "2014": ["a2014.jpg", "b2014.jpg", "c2014.jpg", "d2014.jpg"],
      "2015": ["a2015.jpg", "b2015.jpg", "c2015.jpg", "d2015.jpg"],
      "2016": ["a2016.jpg", "b2016.jpg", "c2016.jpg", "d2016.jpg"]
    };

    const images = document.querySelectorAll('.randomGallery');

    images.forEach(img => {
      const id = img.getAttribute('data-id');
      const options = variants[id];

      if (options && options.length > 0) {
        const picked = options[Math.floor(Math.random() * options.length)];
        img.src = `images/gallery/thumbs/${picked}?t=${Date.now()}`;
      }
    });
  });
</script> -->

			  </script>
			  
			  <!-- <script>
				window.addEventListener('load', () => {
				  const variants = {
					"2005": ["2005.jpg", "a2005.jpg", "b2005.jpg", "c2005.jpg"],
					"2006": ["2006.jpg", "a2006.jpg", "b2006.jpg", "c2006.jpg"],
					"2007": ["2007.jpg", "a2007.jpg", "b2007.jpg", "c2007.jpg"],
					"2008": ["2008.jpg", "a2008.jpg", "b2008.jpg", "c2008.jpg"]
				  };
			  
				  Object.keys(variants).forEach(id => {
					const img = document.querySelector(`.randomGallery${id}`);
					if (img) {
					  const picked = variants[id][Math.floor(Math.random() * variants[id].length)];
					  img.src = `images/gallery/thumbs/${picked}?t=${new Date().getTime()}`;
					}
				  });
				});
			  </script> -->
			  <script>
				window.addEventListener('load', () => {
				  const variants = {
					"2005": ["2005.jpg", "a2005.jpg", "b2005.jpg", "c2005.jpg","d2005.jpg"],
					"2006": ["2006.jpg", "a2006.jpg", "b2006.jpg", "c2006.jpg","d2006.jpg"],
					"2007": ["2007.jpg", "a2007.jpg", "b2007.jpg", "c2007.jpg","d2007.jpg"],
					"2008": ["2008.jpg", "a2008.jpg", "b2008.jpg", "c2008.jpg","d2008.jpg"]
				  };
			  
				  Object.keys(variants).forEach(id => {
					const img = document.querySelector(`.randomGallery${id}`);
					if (img) {
					  const picked = variants[id][Math.floor(Math.random() * variants[id].length)];
					  img.src = `images/gallery/thumbs/${picked}?t=${new Date().getTime()}`;
					}
				  });
				});
			  </script>
			  
			  <script>
				window.addEventListener('load', () => {
				  const variants = {
					"2013": ["a2013.jpg", "b2013.jpg", "c2013.jpg", "d2013.jpg"],
					"2014": ["a2014.jpg", "b2014.jpg", "c2014.jpg", "d2014.jpg"],
					"2015": ["a2015.jpg", "b2015.jpg", "c2015.jpg", "d2015.jpg"],
					"2016": ["a2016.jpg", "b2016.jpg", "c2016.jpg", "d2016.jpg"]
				  };
			  
				  const images = document.querySelectorAll('.randomGallery');
			  
				  images.forEach(img => {
					const id = img.getAttribute('data-id');
					const options = variants[id];
			  
					if (options && options.length > 0) {
					  const picked = options[Math.floor(Math.random() * options.length)];
					  const newSrc = `images/gallery/thumbs/${picked}?t=${Date.now()}`;
					  img.setAttribute('src', newSrc);
					}
				  });
				});
			  </script>
			  <script>
				window.addEventListener('load', () => {
				  const variants = {
					"3013": ["a3013.jpg", "b3013.jpg", "c3013.jpg", "d3013.jpg"],
					"3014": ["a3014.jpg", "b3014.jpg", "c3014.jpg", "d3014.jpg"],
					"3015": ["a3015.jpg", "b3015.jpg", "c3015.jpg", "d3015.jpg"],
					"3016": ["a3016.jpg", "b3016.jpg", "c3016.jpg", "d3016.jpg"]
				  };
			  
				  const images = document.querySelectorAll('.randomGallery');
			  
				  images.forEach(img => {
					const id = img.getAttribute('data-id');
					const options = variants[id];
			  
					if (options && options.length > 0) {
					  const picked = options[Math.floor(Math.random() * options.length)];
					  img.src = `images/gallery/thumbs/${picked}?t=${Date.now()}`;
					}
				  });
				});
			  </script>
<!-- ChatBot Code starts Here -->


<!-- New Code Ends Here -->			  
	</body>
</html>