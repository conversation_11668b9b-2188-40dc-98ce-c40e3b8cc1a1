<?php
session_start();
ini_set('display_errors', 1);
error_reporting(E_ALL);

if (!isset($_SESSION['employee_id'])) {
    header("Location: signin.php");
    exit();
}

$emp_id   = $_SESSION['employee_id'];
$emp_name = $_SESSION['username'];
?>
<!DOCTYPE html>
<html>
<head>
    <title>Timesheet | Employee Portal</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <style>
        body {
            min-height: 100vh;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        .main-card {
            width: 100%;
            max-width: 520px;
            margin: 0 auto;
            padding: 2.4rem 2.2rem 1.4rem 2.2rem;
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(0,0,0,0.11);
            border: none;
        }
        .card-title {
            font-size: 2.15rem;
            font-weight: bold;
            margin-bottom: 1.4rem;
            color: #2976d8;
            text-align: center;
            letter-spacing: .03em;
        }
        .form-label {
            font-size: 1.14rem;
            color: #0d1954;
        }
        .form-control {
            font-size: 1.1rem;
            padding: 0.89rem 0.9rem;
        }
        .btn {
            font-size: 1.12rem;
            padding: 0.88rem;
        }
        .btn-success {
            background: #26cd75;
        }
        .btn-danger {
            background: #f1554a;
        }
        .mb-3,
        .mb-4 {
            margin-bottom: 1.35rem !important;
        }
        @media (max-width:480px) {
            .main-card {
                max-width: 99vw;
                padding: 1.3rem 0.5rem;
            }
            .card-title {
                font-size: 1.19rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-card">

        <div class="card-title">Timesheet</div>
        <div style="color:#0d1954; margin-bottom:.6rem;">
            <b>Welcome,</b> <?= htmlspecialchars($emp_name); ?> (<b><?= htmlspecialchars($emp_id); ?></b>)
        </div>

        <?php if (isset($_GET['msg'])): ?>
            <div class="alert alert-success mb-3"><?= htmlspecialchars($_GET['msg']); ?></div>
        <?php endif; ?>

        <form method="post" action="clock_in_out.php" autocomplete="off" id="clockForm">

            <div class="mb-3">
                <label class="form-label">Employee ID</label>
                <input class="form-control" value="<?= htmlspecialchars($emp_id); ?>" disabled>
            </div>

            <div class="mb-3">
                <label class="form-label">Name</label>
                <input class="form-control" value="<?= htmlspecialchars($emp_name); ?>" disabled>
            </div>

            <div class="mb-3">
                <label class="form-label">Task</label>
                <input class="form-control" type="text" name="task" id="task-input" required>
            </div>

            <input type="hidden" name="emp_id" value="<?= htmlspecialchars($emp_id); ?>">
            <input type="hidden" name="emp_name" value="<?= htmlspecialchars($emp_name); ?>">

            <div class="d-flex gap-2 mb-3">
                <button class="btn btn-success w-50" type="submit" name="action" value="clock_in" id="clock-in-btn">Clock In</button>
                <button class="btn btn-danger w-50" type="submit" name="action" value="clock_out" id="clock-out-btn">Clock Out</button>
            </div>
        </form>

        <div class="d-flex justify-content-between mt-2">
            <a href="fetch_logs.php" class="btn btn-outline-primary btn-sm">View Logs</a>

            <?php if (!empty($_SESSION['is_admin'])): ?>
                <a href="admin_dashboard.php" class="btn btn-outline-warning btn-sm">Admin Dashboard</a>
            <?php endif; ?>

            <a href="logout.php" class="btn btn-outline-secondary btn-sm">Logout</a>
        </div>
    </div>

<script>
    // Make Task required only for Clock In button click
    document.getElementById('clock-out-btn').addEventListener('click', function() {
        document.getElementById('task-input').removeAttribute('required');
    });
    document.getElementById('clock-in-btn').addEventListener('click', function() {
        document.getElementById('task-input').setAttribute('required', 'required');
    });
</script>
</body>
</html>
