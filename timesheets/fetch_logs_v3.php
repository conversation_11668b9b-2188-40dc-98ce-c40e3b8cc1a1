<?php
session_start();
ini_set('display_errors', 1);
error_reporting(E_ALL);
date_default_timezone_set('Asia/Kolkata');
require 'db.php';

if (!isset($_SESSION['employee_id'])) {
    header('Location: signin.php');
    exit();
}

$emp_id = $_SESSION['employee_id'];
$selected_date = $_GET['date'] ?? "";

// Basic query for just THIS user's logs by date (if provided)
$query = "SELECT * FROM employee_logs WHERE emp_id = ?";
$params = [$emp_id];
$types = "s";
if ($selected_date) {
    $query .= " AND date = ?";
    $params[] = $selected_date;
    $types .= "s";
}
$query .= " ORDER BY date DESC, start_time DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

// Calculate total worked seconds for this user (filtered period)
$total_seconds = 0;
$logs = [];
while ($row = $result->fetch_assoc()) {
    $total_seconds += (float)$row['seconds_worked'];
    $logs[] = $row;
}
function format_seconds($seconds) {
    $h = floor($seconds / 3600);
    $m = floor(($seconds % 3600) / 60);
    $s = $seconds % 60;
    return sprintf('%02d:%02d:%02d', $h, $m, $s);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>My Logs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        .container { max-width: 900px; margin: 20px auto; background: #fff; padding: 20px; border-radius: 12px; box-shadow: 0 0 20px rgba(0,0,0,0.1);}
        .logs { max-height: 450px; overflow-y: auto; }
        .btn { font-weight: 500; }
        input[type="date"] { font-size:1.1rem; }
    </style>
</head>
<body>
<div class="container">
    <h2 class="mb-4 text-center">My Work Logs</h2>
    <form method="get" class="row g-2 mb-3 align-items-center">
        <div class="col-auto">
            <input type="date" name="date" class="form-control" value="<?= htmlspecialchars($selected_date) ?>" placeholder="Select date">
        </div>
        <div class="col-auto">
            <button class="btn btn-primary" type="submit">Filter</button>
        </div>
        <div class="col-auto">
            <a href="fetch_logs.php" class="btn btn-secondary">Reset</a>
        </div>
        <div class="col-auto">
            <a href="timesheet.php" class="btn btn-dark">Back</a>
        </div>
    </form>
    <?php if ($logs): ?>
        <div class="alert alert-info mb-2">
            <b>Total work:</b> <?= format_seconds($total_seconds) ?> (hh:mm:ss)
        </div>
    <?php endif; ?>
    <div class="logs">
        <table class="table table-striped table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>Date</th>
                    <th>Task</th>
                    <th>Start Time (IST)</th>
                    <th>End Time (IST)</th>
                    <th>Hours Worked</th>
                    <th>Seconds Worked</th>
                </tr>
            </thead>
            <tbody>
            <?php if (!$logs): ?>
                <tr><td colspan="6" class="text-center">No records found.</td></tr>
            <?php else: foreach ($logs as $row): ?>
                <tr>
                    <td><?= htmlspecialchars($row['date']) ?></td>
                    <td><?= htmlspecialchars($row['task']) ?></td>
                    <td><?= htmlspecialchars($row['start_time']) ?> IST</td>
                    <td><?= htmlspecialchars($row['end_time']) ?> IST</td>
                    <td><?= htmlspecialchars($row['hours_worked']) ?></td>
                    <td><?= htmlspecialchars($row['seconds_worked']) ?></td>
                </tr>
            <?php endforeach; endif; ?>
            </tbody>
        </table>
    </div>
</div>
</body>
</html>
