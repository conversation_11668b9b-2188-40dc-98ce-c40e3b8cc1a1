<?php
session_start();
ini_set('display_errors', 1);
error_reporting(E_ALL);
date_default_timezone_set('Asia/Kolkata');
require 'db.php';

if (!isset($_SESSION['employee_id'])) {
    header('Location: signin.php');
    exit();
}

$is_admin = !empty($_SESSION['is_admin']);
if ($is_admin) {
    $employees = [];
    $res = $conn->query("SELECT employee_id, username FROM employee ORDER BY username");
    while ($row = $res->fetch_assoc()) $employees[] = $row;
}

$selected_emp_id = $_GET['employee_id'] ?? $_SESSION['employee_id'];
$selected_date = $_GET['date'] ?? '';
$show_all = $is_admin && $selected_emp_id === 'all';

$query = "SELECT * FROM employee_logs";
$where = [];
$params = [];
$param_types = "";

if ($show_all) {
    if ($selected_date !== '') {
        $where[] = "date = ?";
        $params[] = $selected_date;
        $param_types .= 's';
    }
} elseif ($is_admin) {
    $where[] = "emp_id = ?";
    $params[] = $selected_emp_id;
    $param_types .= 's';
    if ($selected_date !== '') {
        $where[] = "date = ?";
        $params[] = $selected_date;
        $param_types .= 's';
    }
} else {
    $where[] = "emp_id = ?";
    $params[] = $_SESSION['employee_id'];
    $param_types .= 's';
    if ($selected_date !== '') {
        $where[] = "date = ?";
        $params[] = $selected_date;
        $param_types .= 's';
    }
}

if (!empty($where)) {
    $query .= ' WHERE ' . implode(' AND ', $where);
}
$query .= ' ORDER BY date DESC, start_time DESC';

$stmt = $conn->prepare($query);
if (!empty($param_types)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Employee Logs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        .container { max-width: 1000px; margin: 20px auto; background: #fff; padding: 20px; border-radius: 12px; box-shadow: 0 0 20px rgba(0,0,0,0.1);}
        .logs { max-height: 450px; overflow-y: auto; }
    </style>
</head>
<body>
<div class="container">
    <h2 class="mb-4 text-center">Employee Logs</h2>
    <form method="get" class="row g-3 align-items-center mb-3">
        <?php if ($is_admin): ?>
            <div class="col-md-4">
                <select name="employee_id" class="form-select">
                    <option value="all" <?= ($selected_emp_id == 'all') ? 'selected' : '' ?>>All Employees</option>
                    <?php foreach ($employees as $emp): ?>
                        <option value="<?= htmlspecialchars($emp['employee_id']) ?>" <?= ($selected_emp_id == $emp['employee_id']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($emp['username']) ?> (<?= htmlspecialchars($emp['employee_id']) ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        <?php endif; ?>
        <div class="col-md-3">
            <input type="date" name="date" class="form-control" value="<?= htmlspecialchars($selected_date) ?>">
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary w-100">Filter</button>
        </div>
        <div class="col-md-2">
            <a href="fetch_logs.php" class="btn btn-secondary w-100">Reset</a>
        </div>
        <div class="col-md-1">
            <a href="timesheet.php" class="btn btn-dark w-100">Back</a>
        </div>
    </form>
    <div class="logs">
        <table class="table table-striped table-hover">
            <thead class="table-light">
                <tr>
                    <th>Date</th>
                    <th>Employee Name</th>
                    <th>Employee ID</th>
                    <th>Task</th>
                    <th>Start Time (IST)</th>
                    <th>End Time (IST)</th>
                    <th>Hours Worked</th>
                    <th>Seconds Worked</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($result->num_rows == 0): ?>
                    <tr><td colspan="8" class="text-center">No records found.</td></tr>
                <?php else: ?>
                    <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td><?= htmlspecialchars($row['date']) ?></td>
                            <td><?= htmlspecialchars($row['emp_name']) ?></td>
                            <td><?= htmlspecialchars($row['emp_id']) ?></td>
                            <td><?= htmlspecialchars($row['task']) ?></td>
                            <td><?= htmlspecialchars($row['start_time']) ?> IST</td>
                            <td><?= htmlspecialchars($row['end_time']) ?> IST</td>
                            <td><?= htmlspecialchars($row['hours_worked']) ?></td>
                            <td><?= htmlspecialchars($row['seconds_worked']) ?></td>
                        </tr>
                    <?php endwhile; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
</body>
</html>
