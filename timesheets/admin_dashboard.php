<?php
include 'session_timer.php';
session_start();

ini_set('display_errors', 1);
error_reporting(E_ALL);
date_default_timezone_set('Asia/Kolkata');
require 'db.php';

// Only allow admin
if (
    empty($_SESSION['employee_id']) ||
    empty($_SESSION['is_admin']) ||
    $_SESSION['is_admin'] !== true
) {
    header('Location: signin.php?msg=Access+Denied');
    exit();
}

// Filters and inputs
$search_emp_id = $_GET['emp_id'] ?? 'all';
$period = $_GET['period'] ?? 'all';
$custom_start = $_GET['start_date'] ?? '';
$custom_end = $_GET['end_date'] ?? '';
$where = [];
$params = [];
$types = "";

// Employees dropdown
$employees = [];
$emp_sql = "SELECT employee_id, username FROM employee ORDER BY username";
$emp_res = $conn->query($emp_sql);
while ($row = $emp_res->fetch_assoc()) $employees[] = $row;

// Filter by employee
if ($search_emp_id != 'all') {
    $where[] = "emp_id = ?";
    $params[] = $search_emp_id;
    $types .= 's';
}

// Filter by date/period
$today = date('Y-m-d');
switch ($period) {
    case 'today':
        $where[] = "date = ?";
        $params[] = $today;
        $types .= "s";
        break;
    case 'weekly':
        $start = date('Y-m-d', strtotime('monday this week'));
        $end = date('Y-m-d', strtotime('sunday this week'));
        $where[] = "date BETWEEN ? AND ?";
        $params[] = $start; $params[] = $end;
        $types .= "ss";
        break;
    case 'monthly':
        $start = date('Y-m-01');
        $end = date('Y-m-t');
        $where[] = "date BETWEEN ? AND ?";
        $params[] = $start; $params[] = $end;
        $types .= "ss";
        break;
    case 'yearly':
        $start = date('Y-01-01');
        $end = date('Y-12-31');
        $where[] = "date BETWEEN ? AND ?";
        $params[] = $start; $params[] = $end;
        $types .= "ss";
        break;
    case 'custom':
        if ($custom_start && $custom_end) {
            $where[] = "date BETWEEN ? AND ?";
            $params[] = $custom_start;
            $params[] = $custom_end;
            $types .= "ss";
        }
        break;
}

// Build query
$sql = "SELECT * FROM employee_logs";
if (!empty($where)) $sql .= " WHERE " . implode(" AND ", $where);
$sql .= " ORDER BY date DESC, start_time DESC";
$stmt = $conn->prepare($sql);
if ($types) $stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

// Totals calculation
$total_seconds = 0;
$logs = [];
while ($row = $result->fetch_assoc()) {
    $total_seconds += (float)$row['seconds_worked'];
    $logs[] = $row;
}
function format_seconds($seconds) {
    $h = floor($seconds / 3600);
    $m = floor(($seconds % 3600) / 60);
    $s = $seconds % 60;
    return sprintf('%02d:%02d:%02d', $h, $m, $s);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8"/>
<title>Admin Dashboard | Employee Timesheet</title>
<meta name="viewport" content="width=device-width,initial-scale=1"/>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
<style>
    .container { max-width: 1100px; margin: 24px auto; background: #fff; padding: 18px 26px 32px 26px; border-radius: 15px; box-shadow: 0 5px 25px rgba(0,0,0,0.09);}
</style>
</head>
<body>
<div class="container">
    <h2 class="mb-2 text-primary">Admin Dashboard - Employee Logs</h2>
    <div class="alert alert-warning mb-3 fw-bold">
        You are logged in with admin privileges.
    </div>
    <div class="mb-3">
        <span class="fw-bold text-primary">Welcome Admin:</span>
        <?= htmlspecialchars($_SESSION['username']) . " (" . htmlspecialchars($_SESSION['employee_id']) . ")" ?>
    </div>
    <form method="get" class="row g-3 align-items-end mb-2">
        <div class="col-md-3">
            <label class="form-label fw-bold">Employee</label>
            <select name="emp_id" class="form-select">
                <option value="all" <?= ($search_emp_id=='all')?'selected':''; ?>>All Employees</option>
                <?php foreach ($employees as $emp): ?>
                    <option value="<?= htmlspecialchars($emp['employee_id']) ?>" <?= $search_emp_id == $emp['employee_id'] ? 'selected' : '' ?>>
                        <?= htmlspecialchars($emp['username']) ?> (<?= htmlspecialchars($emp['employee_id']) ?>)
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label fw-bold">Period</label>
            <select name="period" id="period" class="form-select">
                <option value="all" <?= $period=="all"?'selected':'' ?>>All</option>
                <option value="today" <?= $period=="today"?'selected':'' ?>>Today</option>
                <option value="weekly" <?= $period=="weekly"?'selected':'' ?>>Weekly</option>
                <option value="monthly" <?= $period=="monthly"?'selected':'' ?>>Monthly</option>
                <option value="yearly" <?= $period=="yearly"?'selected':'' ?>>Yearly</option>
                <option value="custom" <?= $period=="custom"?'selected':'' ?>>CustomDate</option>
            </select>
        </div>
        <div class="col-md-4" id="custom-date-range" style="display:<?= $period=='custom' ? 'block':'none'; ?>;">
            <div class="row gx-2">
                <div class="col">
                    <label class="form-label fw-bold mb-1">Start Date</label>
                    <input type="date" class="form-control" name="start_date" value="<?= htmlspecialchars($custom_start) ?>">
                </div>
                <div class="col">
                    <label class="form-label fw-bold mb-1">End Date</label>
                    <input type="date" class="form-control" name="end_date" value="<?= htmlspecialchars($custom_end) ?>">
                </div>
            </div>
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">Filter</button>
            <a href="admin_dashboard.php" class="btn btn-outline-secondary">Reset</a>
            <a href="logout.php" class="btn btn-outline-dark ms-2">Logout</a>
        </div>
    </form>
    <?php if ($logs): ?>
        <div class="alert alert-info my-3">
            <b>Total work for selection:</b> <?= format_seconds($total_seconds) ?> (hh:mm:ss)
        </div>
    <?php endif; ?>
    <div style="overflow-x:auto;">
        <table class="table table-bordered table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>Date</th>
                    <th>Employee Name</th>
                    <th>Employee ID</th>
                    <th>Task</th>
                    <th>Start Time (IST)</th>
                    <th>End Time (IST)</th>
                    <th>Hours Worked</th>
                    <th>Seconds Worked</th>
                </tr>
            </thead>
            <tbody>
            <?php if (!$logs): ?>
                <tr><td colspan="8" class="text-center text-muted">No logs found.</td></tr>
            <?php else: foreach ($logs as $log): ?>
                <tr>
                    <td><?= htmlspecialchars($log['date']) ?></td>
                    <td><?= htmlspecialchars($log['emp_name']) ?></td>
                    <td><?= htmlspecialchars($log['emp_id']) ?></td>
                    <td><?= htmlspecialchars($log['task']) ?></td>
                    <td><?= htmlspecialchars($log['start_time']) ?> IST</td>
                    <td><?= htmlspecialchars($log['end_time']) ?> IST</td>
                    <td><?= htmlspecialchars($log['hours_worked']) ?></td>
                    <td><?= htmlspecialchars($log['seconds_worked']) ?></td>
                </tr>
            <?php endforeach; endif; ?>
            </tbody>
        </table>
    </div>
</div>
<script>
document.getElementById('period').addEventListener('change', function() {
    document.getElementById('custom-date-range').style.display = (this.value === 'custom') ? 'block' : 'none';
});
</script>
</body>
</html>
