<?php
include 'session_timer.php';
session_start();

date_default_timezone_set('Asia/Kolkata');
ini_set('display_errors', 1);
error_reporting(E_ALL);

if (!isset($_SESSION['employee_id'], $_SESSION['username'])) {
    header('Location: signin.php');
    exit();
}

$clocked_in  = isset($_SESSION['start_time']);
$emp_id      = $_SESSION['employee_id'];
$emp_name    = $_SESSION['username'];
$msg         = isset($_GET['msg']) ? htmlspecialchars($_GET['msg']) : "";
$is_admin    = !empty($_SESSION['is_admin']) && $_SESSION['is_admin'];

$clockin_disabled   = $clocked_in ? 'disabled' : '';
$task_disabled      = $clocked_in ? 'readonly' : '';
$clockout_disabled  = $clocked_in ? '' : 'disabled';
$viewlog_disabled   = $clocked_in ? 'disabled' : '';
$task_value         = $clocked_in && isset($_SESSION['task']) ? htmlspecialchars($_SESSION['task']) : '';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Timesheet | Employee Portal</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <style>
        body {
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .main-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 6px 30px rgba(0,0,0,.09);
            padding: 2.2rem 2rem 1.2rem 2rem;
            width: 100%;
            max-width: 420px;
            /* Admin see colored border & bg */
            <?php if ($is_admin): ?>
            border: 3px solid #ffb021;
            background: #fffbe4 !important;
            <?php endif; ?>
        }
        .btn-admin:disabled, .btn-admin.disabled {
            opacity: 0.62 !important;
            pointer-events: none !important;
        }
    </style>
</head>
<body>
<div class="main-card">
    <div style="color:#2976d8; font-size:1.13rem; font-weight:700; letter-spacing:1px;">
        Timesheet
        <?php if ($is_admin): ?>
            <span class="badge bg-warning text-dark ms-2" style="font-size:1rem;">ADMIN ACCOUNT</span>
        <?php endif; ?>
    </div>
    <div style="color:#0d1954; margin-bottom:.6rem;">
        <b>Welcome,</b>
        <?= htmlspecialchars($emp_name); ?>
        (<b><?= htmlspecialchars($emp_id); ?></b>)
        <?php if ($is_admin): ?>
            <span class="badge rounded-pill bg-danger ms-2 fw-semibold" title="You have admin rights">Superuser</span>
        <?php endif; ?>
    </div>
    <?php if($msg): ?>
        <div class="alert alert-success mb-2 p-2"><?= $msg ?></div>
    <?php endif; ?>
    <form method="post" action="clock_in_out.php" autocomplete="off" id="clockForm">
        <div class="mb-3">
            <label class="form-label">Employee ID</label>
            <input class="form-control" value="<?= htmlspecialchars($emp_id); ?>" disabled>
        </div>
        <div class="mb-3">
            <label class="form-label">Name</label>
            <input class="form-control" value="<?= htmlspecialchars($emp_name); ?>" disabled>
        </div>
        <div class="mb-3">
            <label class="form-label">Task</label>
            <input class="form-control" type="text" name="task" id="task-input"
                   value="<?= $task_value ?>" required <?= $task_disabled ?>>
        </div>
        <input type="hidden" name="emp_id" value="<?= htmlspecialchars($emp_id); ?>">
        <input type="hidden" name="emp_name" value="<?= htmlspecialchars($emp_name); ?>">
        <div class="d-flex gap-2 mb-3">
            <button class="btn btn-success w-50" type="submit" name="action" value="clock_in" id="clock-in-btn" <?= $clockin_disabled ?>>Clock In</button>
            <button class="btn btn-danger w-50" type="submit" name="action" value="clock_out" id="clock-out-btn" <?= $clockout_disabled ?>>Clock Out</button>
        </div>
    </form>

    <!-- ADMIN: Admin Dashboard only if NOT clocked in -->
    <?php if ($is_admin): ?>
        <div class="mt-3 mb-2">
            <a href="admin_dashboard.php"
               class="btn btn-warning w-100 fw-bold btn-admin <?= $clocked_in ? 'disabled' : '' ?>"
               tabindex="<?= $clocked_in ? '-1' : '0' ?>"
               aria-disabled="<?= $clocked_in ? 'true' : 'false' ?>"
            >
                <span class="me-1">&#9881;</span> Admin Dashboard
            </a>
            <?php if ($clocked_in): ?>
                <div class="small text-danger mt-1" style="font-weight:500;">
                    Clock out to access admin features.
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="d-flex gap-2 mt-2">
        <!-- Admin sees View Logs as their own log only -->
        <a href="fetch_logs.php" class="btn btn-outline-primary flex-fill <?= $viewlog_disabled ?>">View Logs</a>
        <a href="logout.php" class="btn btn-outline-secondary flex-fill">Logout</a>
    </div>
</div>
</body>
</html>
