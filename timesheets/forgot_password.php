<?php
include 'session_timer.php';
session_start();
require 'db.php';

$step = 1;
$msg = "";
$error = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Step 1: Enter Employee ID and Email
    if (isset($_POST['step']) && $_POST['step'] == 1) {
        $employee_id = trim($_POST['employee_id']);
        $email = trim($_POST['email']);
        $stmt = $conn->prepare("SELECT id FROM employee WHERE employee_id=? AND email=? LIMIT 1");
        $stmt->bind_param("ss", $employee_id, $email);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($user = $result->fetch_assoc()) {
            // Generate secure one-time token & store with expiry (session only)
            $_SESSION['reset_token'] = bin2hex(random_bytes(16));
            $_SESSION['reset_emp_id'] = $employee_id;
            $_SESSION['reset_token_time'] = time();
            $step = 2;
        } else {
            $error = "Invalid Employee ID or Email.";
        }
        $stmt->close();
    }
    // Step 2: Set New Password
    else if (isset($_POST['step']) && $_POST['step'] == 2) {
        // Security: Token must be valid and not expired
        if (empty($_SESSION['reset_token']) || empty($_SESSION['reset_emp_id']) || empty($_SESSION['reset_token_time']) ||
            time() - $_SESSION['reset_token_time'] > 900) // 15 min expiry
        {
            $error = "Reset request expired. Please try again.";
            unset($_SESSION['reset_token'], $_SESSION['reset_emp_id'], $_SESSION['reset_token_time']);
            $step = 1;
        } else {
            $new_pass = trim($_POST['password'] ?? '');
            $conf_pass = trim($_POST['cpassword'] ?? '');
            if ($new_pass === "" || $conf_pass === "") {
                $error = "Please fill all password fields.";
                $step = 2;
            } elseif ($new_pass !== $conf_pass) {
                $error = "Passwords do not match.";
                $step = 2;
            } elseif (strlen($new_pass) < 6) {
                $error = "Password must be at least 6 characters.";
                $step = 2;
            } else {
                // Save new password hash in DB
                $new_hash = password_hash($new_pass, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE employee SET password=? WHERE employee_id=?");
                $stmt->bind_param("ss", $new_hash, $_SESSION['reset_emp_id']);
                $stmt->execute();
                $stmt->close();
                unset($_SESSION['reset_token'], $_SESSION['reset_emp_id'], $_SESSION['reset_token_time']);
                $_SESSION['message'] = "Password reset successful. Please sign in with your new password.";
                header("Location: signin.php"); exit();
            }
        }
    }
} else {
    // If there's a valid token, default step to 2 for refresh
    if (isset($_SESSION['reset_token'], $_SESSION['reset_emp_id'], $_SESSION['reset_token_time']) && 
        time() - $_SESSION['reset_token_time'] <= 900) 
    {
        $step = 2;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>Forgot Password | Timesheet</title>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"/>
    <style>
    body {
        min-height: 100vh;
        background: #f8f9fa;
        display:flex;
        align-items:center;
        justify-content:center;
        font-family: 'Segoe UI', Arial, sans-serif;
    }
    .reset-card {
        max-width: 430px;
        background: #fff;
        box-shadow: 0 8px 32px rgba(0,0,0,0.09);
        padding: 2.3rem 2rem;
        border-radius: 18px;
        width:100%;
    }
    .form-label { font-weight:600; color: #071E54; font-size:1.13rem; }
    .btn-primary { font-size:1.08rem; padding:0.9rem;}
    .page-title { font-size:1.55rem; font-weight:700; color:#2976d8; margin-bottom:1.3rem;}
    @media(max-width:540px){ .reset-card{max-width:99vw; padding:1rem .4rem;} }
    </style>
</head>
<body>
<div class="reset-card">
    <div class="page-title"><?= $step==1 ? 'Forgot Password' : 'Reset Password'; ?></div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger mb-2"><?= htmlspecialchars($error) ?></div>
    <?php endif; ?>

    <?php if ($step == 1): ?>
        <form method="post" autocomplete="off">
            <input type="hidden" name="step" value="1"/>
            <div class="mb-3">
                <label class="form-label">Employee ID</label>
                <input class="form-control" name="employee_id" type="text" required autocomplete="off"/>
            </div>
            <div class="mb-3">
                <label class="form-label">Registered Email</label>
                <input class="form-control" name="email" type="email" required autocomplete="off"/>
            </div>
            <button type="submit" class="btn btn-primary w-100">Continue</button>
        </form>
        <div class="mt-4 text-center"><a href="signin.php">Back to Sign In</a></div>
    <?php elseif ($step == 2): ?>
        <form method="post" autocomplete="off">
            <input type="hidden" name="step" value="2"/>
            <div class="mb-3">
                <label class="form-label">New Password</label>
                <input class="form-control" name="password" type="password" required autocomplete="new-password" minlength="6"/>
            </div>
            <div class="mb-3">
                <label class="form-label">Confirm New Password</label>
                <input class="form-control" name="cpassword" type="password" required autocomplete="new-password" minlength="6"/>
            </div>
            <button type="submit" class="btn btn-primary w-100">Set New Password</button>
        </form>
        <div class="mt-4 text-center"><a href="signin.php">Cancel and Sign In</a></div>
    <?php endif; ?>
</div>
</body>
</html>
