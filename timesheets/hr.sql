-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 19, 2025 at 10:17 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `hr`
--

-- --------------------------------------------------------

--
-- Table structure for table `employee`
--

CREATE TABLE `employee` (
  `id` int(11) NOT NULL,
  `employee_id` varchar(20) NOT NULL,
  `username` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employee`
--

INSERT INTO `employee` (`id`, `employee_id`, `username`, `password`, `phone`, `email`, `is_admin`) VALUES
(1, 'OFS20254001', 'vinod', '$2y$10$CumBjqB4iOfq0C/jFyMje.zz7znjJO1eWqYYVSo2WfeGNiJE3qIH2', '3456734567', '<EMAIL>', 1),
(2, 'OFS20254002', 'deep', '$2y$10$h.pIf9y.DWtprR0YrsZXuOR2Kd4yWjJr8exrZ/KugnJLJeg7wAT3m', '45676879887', '<EMAIL>', 0),
(3, 'OFS20254003', 'hema', '$2y$10$RI5neO2PwKGrh3sOd1oiXOQkm.Y9Dhte3i86Rfy0XUdENDUxJzKgW', '34567890876', '<EMAIL>', 0),
(4, 'OFS20254004', 'zera', '$2y$10$ISZbsPy95pQDafMDvvaGl.FdOZZE3PJlphPm27DT3QJWyCiGDf9I.', '32465783456', '<EMAIL>', 0),
(5, 'OFS20254005', 'varun', '$2y$10$IrTLMyljU4UYB3gR9kH77us75E43tlTreVwC3BKqBGHtK5RrKneDC', '456789433445', '<EMAIL>', 0),
(6, 'OFS20254006', 'ravi', '$2y$10$MUiIjQ7bksNqZU2eXVRvweR/JeQ2sP4eUCG/3akHaCi6YRisdxYxK', '987654324', '<EMAIL>', 0),
(7, 'OFS20254007', 'arun', '$2y$10$7ObulE65WIQkemZne51DV.nGHki4pu1BaYqRTZwSKykmIx2PJz04u', '98765432233', '<EMAIL>', 0),
(8, 'OFS20254008', 'nani', '$2y$10$HhyK44XkBqlt4SBLRKRKo.7ar9tmun2Rk5z7KFYAGdA1cb4kX.Ogm', '9876543345', '<EMAIL>', 0),
(9, 'OFS20254009', 'deva', '$2y$10$o0qm4ITThRyqIaBMFhSy.ekJAfDx9IhEnlY6FMe.4t3JeRj9h3Bbm', '98765432445', '<EMAIL>', 0);

-- --------------------------------------------------------

--
-- Table structure for table `employee_logs`
--

CREATE TABLE `employee_logs` (
  `id` int(11) NOT NULL,
  `emp_id` varchar(20) NOT NULL,
  `emp_name` varchar(100) NOT NULL,
  `task` varchar(255) DEFAULT NULL,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `hours_worked` double NOT NULL,
  `seconds_worked` double NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employee_logs`
--

INSERT INTO `employee_logs` (`id`, `emp_id`, `emp_name`, `task`, `date`, `start_time`, `end_time`, `hours_worked`, `seconds_worked`) VALUES
(1, 'OFS20254001', 'vinod', 'check', '2025-07-29', '00:20:25', '00:20:29', 0, 0),
(2, 'OFS20254002', 'deep', 'retregerg', '2025-07-29', '00:32:57', '00:32:58', 0, 0),
(3, 'OFS20254004', 'zera', 'daad', '2025-07-29', '00:49:16', '00:49:18', 0, 0),
(4, 'OFS20254006', 'ravi', 'work', '2025-08-02', '22:57:47', '22:57:52', 0, 0),
(5, 'OFS20254007', 'arun', 'work', '2025-08-02', '22:59:33', '22:59:37', 0, 0),
(6, 'OFS20254006', 'ravi', 'task', '2025-08-02', '23:16:46', '23:16:51', 0, 0),
(7, 'OFS20254007', 'arun', 'track', '2025-08-02', '23:19:43', '23:19:46', 0, 0),
(8, 'OFS20254008', 'nani', 'work', '2025-08-18', '21:15:12', '21:15:21', 0, 0),
(9, 'OFS20254009', 'deva', 'task', '2025-08-18', '22:33:53', '22:33:59', 0, 0),
(10, 'OFS20254002', 'deep', 'task', '2025-08-19', '02:29:26', '02:29:30', 0, 4),
(11, 'OFS20254002', 'deep', 'work', '2025-08-19', '16:00:40', '16:00:52', 0, 12),
(12, 'OFS20254001', 'vinod', 'bb', '2025-08-19', '16:08:02', '16:08:05', 0, 3),
(13, 'OFS20254001', 'vinod', 'work', '2025-08-19', '16:35:29', '16:35:32', 0, 3),
(14, 'OFS20254002', 'deep', 'work', '2025-08-19', '16:42:39', '16:42:42', 0, 3),
(15, 'OFS20254001', 'vinod', 'work', '2025-08-19', '16:49:35', '16:49:50', 0, 15),
(16, 'OFS20254001', 'vinod', 'work', '2025-08-19', '16:58:39', '16:58:46', 0, 7),
(17, 'OFS20254001', 'vinod', 'task', '2025-08-19', '17:02:54', '17:02:57', 0, 3),
(18, 'OFS20254002', 'deep', 'work', '2025-08-19', '17:03:56', '17:03:58', 0, 2),
(19, 'OFS20254001', 'vinod', 'woo', '2025-08-19', '17:14:49', '17:14:51', 0, 2),
(20, 'OFS20254002', 'deep', 'woo', '2025-08-19', '17:15:46', '17:15:50', 0, 4),
(21, 'OFS20254001', 'vinod', 'work', '2025-08-19', '17:22:38', '17:22:40', 0, 2),
(22, 'OFS20254001', 'vinod', 'f', '2025-08-19', '17:22:46', '17:22:48', 0, 2),
(23, 'OFS20254002', 'deep', 'hi', '2025-08-19', '17:23:47', '17:23:50', 0, 3),
(24, 'OFS20254002', 'deep', 'working', '2025-08-20', '01:41:53', '01:42:25', 0.01, 32);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `employee`
--
ALTER TABLE `employee`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `employee_id` (`employee_id`);

--
-- Indexes for table `employee_logs`
--
ALTER TABLE `employee_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_empid_date` (`emp_id`,`date`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `employee`
--
ALTER TABLE `employee`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `employee_logs`
--
ALTER TABLE `employee_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
