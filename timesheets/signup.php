<?php
include 'session_timer.php';
session_start();

ini_set('display_errors', 1);
error_reporting(E_ALL);
require 'db.php';

// Function to generate new sequential employee IDs like OFS20254001, OFS20254002,...
function generateSequentialEmployeeID($conn) {
    $prefix = "OFS20254";
    $padding = 3;

    $sql = "SELECT employee_id FROM employee WHERE employee_id LIKE '".$prefix."%' ORDER BY employee_id DESC LIMIT 1";
    $result = $conn->query($sql);
    $last_id = $result && $result->num_rows > 0 ? $result->fetch_assoc()['employee_id'] : null;

    if ($last_id) {
        $number_part = substr($last_id, strlen($prefix));
        $next_number = (int)$number_part + 1;
    } else {
        $next_number = 1;
    }
    return $prefix . str_pad($next_number, $padding, "0", STR_PAD_LEFT);
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username']);
    $password_raw = trim($_POST['password']);
    $phone = trim($_POST['phone']);
    $email = trim($_POST['email']);

    // Basic validation can be added here if needed

    if (empty($username) || empty($password_raw) || empty($phone) || empty($email)) {
        $error = "Please fill all the fields.";
    } else {
        $password = password_hash($password_raw, PASSWORD_DEFAULT);
        $employee_id = generateSequentialEmployeeID($conn);

        $stmt = $conn->prepare("INSERT INTO employee (employee_id, username, password, phone, email) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param('sssss', $employee_id, $username, $password, $phone, $email);
        if ($stmt->execute()) {
            $_SESSION['message'] = "Registration successful! Your Employee ID is <strong>{$employee_id}</strong>. Please sign in.";
            header('Location: signin.php');
            exit;
        } else {
            $error = "Error: " . htmlspecialchars($stmt->error);
        }
        $stmt->close();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Employee Signup - Timesheet</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"/>
    <style>
        body {
            min-height: 100vh;
            background: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .signup-card {
            max-width: 480px;
            width: 100%;
            background: #fff;
            padding: 2.5rem 2rem;
            border-radius: 18px;
            box-shadow: 0 12px 20px rgb(0 0 0 / 0.07);
        }
        .page-title {
            font-size: 2.3rem;
            font-weight: 700;
            color: #2976d8;
            margin-bottom: 1.5rem;
            text-align: center;
            letter-spacing: 0.05em;
        }
        label {
            font-weight: 600;
            color: #224057;
        }
        .form-control {
            font-size: 1.15rem;
            padding: 0.8rem 0.9rem;
        }
        .btn-primary {
            font-size: 1.2rem;
            padding: 0.9rem;
        }
        .form-text {
            font-size: 0.9rem;
            margin-top: 0.9rem;
            color: #222;
        }
        .alert {
            font-size: 1rem;
            margin-bottom: 1rem;
        }
        @media (max-width: 480px) {
            .signup-card {
                border-radius: 0;
                box-shadow: none;
                padding: 1.5rem 1rem;
            }
        }
    </style>
</head>
<body>
<div class="signup-card">
    <div class="page-title">Employee Signup</div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
    <?php endif; ?>

    <?php if (!empty($_SESSION['message'])): ?>
        <div class="alert alert-success"><?= $_SESSION['message'] ?></div>
        <?php unset($_SESSION['message']); ?>
    <?php endif; ?>

    <form method="post" autocomplete="off" novalidate>
        <div class="mb-3">
            <label for="username" class="form-label">Full Name</label>
            <input id="username" name="username" type="text" class="form-control" required autocomplete="off" />
        </div>
        <div class="mb-3">
            <label for="password" class="form-label">Password</label>
            <input id="password" name="password" type="password" class="form-control" required autocomplete="new-password" />
        </div>
        <div class="mb-3">
            <label for="phone" class="form-label">Phone Number</label>
            <input id="phone" name="phone" type="text" class="form-control" required autocomplete="off" />
        </div>
        <div class="mb-4">
            <label for="email" class="form-label">Email</label>
            <input id="email" name="email" type="email" class="form-control" required autocomplete="off" />
        </div>
        <button type="submit" class="btn btn-primary w-100">Register</button>
    </form>

    <div class="form-text text-center">
        Already Registered? <a href="signin.php" class="fw-bold text-decoration-underline">Signin Here</a>
    </div>
</div>
</body>
</html>
