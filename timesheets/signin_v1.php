<?php
session_start();
ini_set('display_errors', 1);
error_reporting(E_ALL);
require 'db.php';

$error = '';
// Check and display session message if any
if (!empty($_SESSION['message'])) {
    $message = $_SESSION['message'];
    unset($_SESSION['message']);
} else {
    $message = '';
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $employee_id = trim($_POST['employee_id']);
    $password    = trim($_POST['password']);

    // Prepare and execute query to fetch user info including is_admin
    $stmt = $conn->prepare("SELECT username, password, is_admin FROM employee WHERE employee_id = ?");
    $stmt->bind_param("s", $employee_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        if (password_verify($password, $row['password'])) {
            // Set session variables including is_admin flag (boolean)
            $_SESSION['employee_id'] = $employee_id;
            $_SESSION['username']    = $row['username'];
            $_SESSION['is_admin']    = ($row['is_admin'] == 1); // convert to bool
            // Redirect to timesheet page
            header("Location: timesheet.php");
            exit();
        } else {
            $error = "Invalid password.";
        }
    } else {
        $error = "Employee not found.";
    }
    $stmt->close();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Sign In | Employee Timesheet</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        body {
            min-height: 100vh;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        .main-card {
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            padding: 2.3rem 2rem;
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(0,0,0,0.09);
        }
        .page-title {
            font-size: 2.1rem;
            font-weight: bold;
            margin-bottom: 1.3rem;
            color: #2976d8;
            text-align: center;
            letter-spacing: 0.02em;
        }
        .form-label { font-size: 1.13rem; color: #0d1954; }
        .form-control { font-size: 1.1rem; padding: 0.88rem; }
        .btn-primary { font-size: 1.15rem; padding: 0.89rem; }
    </style>
</head>
<body>
    <div class="main-card">
        <div class="page-title">Employee Sign In</div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-success mb-3"><?= $message // NOTE: No htmlspecialchars here! ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger mb-3"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <form method="post" autocomplete="off" novalidate>
            <div class="mb-3">
                <label class="form-label" for="employee_id">Employee ID</label>
                <input id="employee_id" class="form-control" type="text" name="employee_id" required autocomplete="off" />
            </div>
            <div class="mb-3">
                <label class="form-label" for="password">Password</label>
                <input id="password" class="form-control" type="password" name="password" required autocomplete="new-password" />
            </div>

            <!-- Added forgot password link -->
            <div class="text-end mb-2">
                <a href="forgot_password.php" class="fw-semibold">Forgot password?</a>
            </div>

            <button class="btn btn-primary w-100" type="submit">Sign In</button>
        </form>
        <div class="mt-4 text-center">
            New User? <a href="signup.php" class="fw-bold text-decoration-underline">Sign up here</a>
        </div>
    </div>
</body>
</html>
