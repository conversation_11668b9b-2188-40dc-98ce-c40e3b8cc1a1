<?php
session_start();
ini_set('display_errors', 1);
error_reporting(E_ALL);

require 'db.php';

// Only allow POST requests to this script
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: timesheet.php?msg=' . urlencode('Invalid access method.'));
    exit();
}

// Check if user is logged in
if (!isset($_SESSION['employee_id'])) {
    header('Location: signin.php');
    exit();
}

// Sanitize POST inputs
$emp_id = $_POST['emp_id'] ?? '';
$emp_name = $_POST['emp_name'] ?? '';
$task_input = trim($_POST['task'] ?? '');
$date = date('Y-m-d');

// Validate mandatory fields
if (!$emp_id || !$emp_name) {
    header('Location: timesheet.php?msg=' . urlencode('Employee information missing.'));
    exit();
}

// Handle clock in action
if (isset($_POST['action']) && $_POST['action'] === 'clock_in') {
    if ($task_input === '') {
        header('Location: timesheet.php?msg=' . urlencode('Please enter a task before clocking in.'));
        exit();
    }

    // Save task and start time in session
    $_SESSION['task'] = $task_input;
    $_SESSION['start_time'] = time();

    // Redirect with success message showing clock-in time
    header('Location: timesheet.php?msg=' . urlencode('Clocked in at ' . date('H:i:s', $_SESSION['start_time'])));
    exit();
}

// Handle clock out action
if (isset($_POST['action']) && $_POST['action'] === 'clock_out') {
    // Ensure there was a clock-in before
    if (!isset($_SESSION['start_time']) || !isset($_SESSION['task'])) {
        header('Location: timesheet.php?msg=' . urlencode('You must clock in before clocking out.'));
        exit();
    }

    $start_time = $_SESSION['start_time'];
    $task = $_SESSION['task'];
    $end_time = time();

    // Calculate total hours worked with precision (two decimals)
    $seconds_worked = $end_time - $start_time;
    $hours_worked = round($seconds_worked / 3600, 2);

    // Format times for DB storage (HH:MM:SS)
    $start_time_formatted = date('H:i:s', $start_time);
    $end_time_formatted = date('H:i:s', $end_time);

    // Insert record to database
    $stmt = $conn->prepare("INSERT INTO employee_logs (emp_id, emp_name, task, date, start_time, end_time, hours_worked) VALUES (?, ?, ?, ?, ?, ?, ?)");
    if (!$stmt) {
        // Prepare failed, log error or display message
        header('Location: timesheet.php?msg=' . urlencode('Database error: Could not prepare statement.'));
        exit();
    }

    $stmt->bind_param('ssssssd', $emp_id, $emp_name, $task, $date, $start_time_formatted, $end_time_formatted, $hours_worked);

    if (!$stmt->execute()) {
        // Execution failed
        $stmt->close();
        header('Location: timesheet.php?msg=' . urlencode('Database error: Could not save log.'));
        exit();
    }

    $stmt->close();

    // Clear session variables related to clock-in
    unset($_SESSION['start_time'], $_SESSION['task']);

    // Redirect with success message showing clock-out time and confirmation
    header('Location: timesheet.php?msg=' . urlencode('Clocked out at ' . $end_time_formatted . '. Work logged successfully.'));
    exit();
}

// If the action parameter is missing or invalid
header('Location: timesheet.php?msg=' . urlencode('Invalid action.'));
exit();
?>
