<?php
session_start();
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Always set THIS at the top!
date_default_timezone_set('Asia/Kolkata');

require 'db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: timesheet.php?msg=Invalid access');
    exit();
}
if (!isset($_SESSION['employee_id'])) {
    header('Location: signin.php');
    exit();
}

$emp_id = $_POST['emp_id'] ?? '';
$emp_name = $_POST['emp_name'] ?? '';
$date = date('Y-m-d');

if ($_POST['action'] === 'clock_in') {
    $task = trim($_POST['task'] ?? '');
    if ($task === '') {
        header('Location: timesheet.php?msg=' . urlencode('Please enter a task before clocking in.'));
        exit();
    }
    // Save start only on clock in!
    $_SESSION['task'] = $task;
    $_SESSION['start_time'] = time();
    header('Location: timesheet.php?msg=' . urlencode('Clocked in at ' . date('H:i:s', $_SESSION['start_time']) . ' IST'));
    exit();
}

if ($_POST['action'] === 'clock_out') {
    if (!isset($_SESSION['start_time']) || !isset($_SESSION['task'])) {
        header('Location: timesheet.php?msg=' . urlencode('You must clock in before clocking out.'));
        exit();
    }

    $start_time = $_SESSION['start_time'];
    $task = $_SESSION['task'];
    $end_time = time();

    $seconds_worked = $end_time - $start_time;
    $hours_worked   = round($seconds_worked / 3600, 2);

    $start_time_fmt = date('H:i:s', $start_time); // IST!
    $end_time_fmt   = date('H:i:s', $end_time);

    // Store both hours and seconds (column seconds_worked must exist!)
    $stmt = $conn->prepare("INSERT INTO employee_logs (emp_id, emp_name, task, date, start_time, end_time, hours_worked, seconds_worked) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param('ssssssdd', $emp_id, $emp_name, $task, $date, $start_time_fmt, $end_time_fmt, $hours_worked, $seconds_worked);
    $stmt->execute();
    $stmt->close();

    unset($_SESSION['start_time'], $_SESSION['task']);
    header('Location: timesheet.php?msg=' . urlencode("Clocked out at $end_time_fmt IST. Worked $seconds_worked seconds."));
    exit();
}

header('Location: timesheet.php?msg=' . urlencode('Something went wrong.'));
exit();
?>
