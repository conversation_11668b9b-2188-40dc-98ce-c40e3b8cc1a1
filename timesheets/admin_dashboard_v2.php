<?php
session_start();
ini_set('display_errors', 1);
error_reporting(E_ALL);
date_default_timezone_set('Asia/Kolkata');
require 'db.php';

// Only admin/HR users allowed
if (empty($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header("Location: signin.php?msg=Access+Denied");
    exit();
}

// --- Filters ---
$search_emp_id = $_GET['emp_id'] ?? '';
$period = $_GET['period'] ?? 'all';
$custom_start = $_GET['start_date'] ?? '';
$custom_end = $_GET['end_date'] ?? '';

$where = [];
$params = [];
$types = "";

// Employee ID filter
if ($search_emp_id !== '') {
    $where[] = "emp_id LIKE ?";
    $params[] = '%' . $search_emp_id . '%';
    $types .= "s";
}

// Date filters
$today = date('Y-m-d');
switch ($period) {
    case 'today':
        $where[] = "date = ?";
        $params[] = $today;
        $types .= "s";
        break;
    case 'weekly':
        $monday = date('Y-m-d', strtotime('monday this week'));
        $sunday = date('Y-m-d', strtotime('sunday this week'));
        $where[] = "date BETWEEN ? AND ?";
        $params[] = $monday;
        $params[] = $sunday;
        $types .= "ss";
        break;
    case 'monthly':
        $month_start = date('Y-m-01');
        $month_end = date('Y-m-t');
        $where[] = "date BETWEEN ? AND ?";
        $params[] = $month_start;
        $params[] = $month_end;
        $types .= "ss";
        break;
    case 'custom':
        if ($custom_start && $custom_end) {
            $where[] = "date BETWEEN ? AND ?";
            $params[] = $custom_start;
            $params[] = $custom_end;
            $types .= "ss";
        }
        break;
    // default: show all
}

$sql = "SELECT * FROM employee_logs";
if (!empty($where)) $sql .= " WHERE " . implode(" AND ", $where);
$sql .= " ORDER BY date DESC, start_time DESC";

$stmt = $conn->prepare($sql);
if ($types) $stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>Admin Dashboard | Employee Timesheet</title>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #f6f7fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 1100px; margin: 24px auto; background: #fff; padding: 18px 26px 32px 26px; border-radius: 15px; box-shadow: 0 5px 25px rgba(0,0,0,0.09);}
        .logs-scroll { max-height: 480px; overflow-y: auto; border: 1px solid #d0d2d6; border-radius: 7px; margin-top: 8px;}
        table { font-size: 0.97rem;}
        @media (max-width: 1000px) { .container{padding:10px;} table {min-width:100vw;} }
    </style>
</head>
<body>
<div class="container">
    <h2 class="mb-3 text-primary">Admin Dashboard - Employee Logs</h2>
    <form class="row g-3 align-items-end mb-2" method="get">
        <div class="col-md-3">
            <label for="emp_id" class="form-label fw-bold mb-1">Employee ID</label>
            <input type="text" class="form-control" name="emp_id" id="emp_id"
                   value="<?= htmlspecialchars($search_emp_id) ?>" placeholder="Partial or full ID">
        </div>
        <div class="col-md-2">
            <label class="form-label fw-bold mb-1" for="period">Period</label>
            <select class="form-select" name="period" id="period">
                <option value="all" <?= $period=="all"?'selected':'' ?>>All</option>
                <option value="today" <?= $period=="today"?'selected':'' ?>>Today</option>
                <option value="weekly" <?= $period=="weekly"?'selected':'' ?>>This Week</option>
                <option value="monthly" <?= $period=="monthly"?'selected':'' ?>>This Month</option>
                <option value="custom" <?= $period=="custom"?'selected':'' ?>>Custom Range</option>
            </select>
        </div>
        <div class="col-md-4" id="custom-date-range" style="display:<?= $period=='custom' ? 'block':'none'; ?>;">
            <div class="row gx-2">
                <div class="col">
                    <label class="form-label fw-bold mb-1">Start Date</label>
                    <input type="date" class="form-control" name="start_date" value="<?= htmlspecialchars($custom_start) ?>">
                </div>
                <div class="col">
                    <label class="form-label fw-bold mb-1">End Date</label>
                    <input type="date" class="form-control" name="end_date" value="<?= htmlspecialchars($custom_end) ?>">
                </div>
            </div>
        </div>
        <div class="col-md-2 d-grid">
            <button type="submit" class="btn btn-primary">Filter</button>
        </div>
        <div class="col-md-1 d-grid">
            <a href="admin_dashboard.php" class="btn btn-outline-secondary" title="Reset Filters">Reset</a>
        </div>
    </form>
    <div class="logs-scroll">
        <table class="table table-bordered table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>Date</th>
                    <th>Employee Name</th>
                    <th>Employee ID</th>
                    <th>Task</th>
                    <th>Start Time (IST)</th>
                    <th>End Time (IST)</th>
                    <th>Hours Worked</th>
                    <th>Seconds Worked</th>
                </tr>
            </thead>
            <tbody>
            <?php if ($result->num_rows === 0): ?>
                <tr><td colspan="8" class="text-center text-muted">No logs found.</td></tr>
            <?php else: ?>
                <?php while ($log = $result->fetch_assoc()): ?>
                    <tr>
                        <td><?= htmlspecialchars($log['date']) ?></td>
                        <td><?= htmlspecialchars($log['emp_name']) ?></td>
                        <td><?= htmlspecialchars($log['emp_id']) ?></td>
                        <td><?= htmlspecialchars($log['task']) ?></td>
                        <td><?= htmlspecialchars($log['start_time']) ?> IST</td>
                        <td><?= htmlspecialchars($log['end_time']) ?> IST</td>
                        <td><?= htmlspecialchars($log['hours_worked']) ?></td>
                        <td><?= htmlspecialchars($log['seconds_worked']) ?></td>
                    </tr>
                <?php endwhile; ?>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
    <div class="mt-3 d-flex justify-content-end">
        <a href="timesheet.php" class="btn btn-outline-dark btn-sm me-2">Back to Timesheet</a>
        <a href="logout.php" class="btn btn-outline-secondary btn-sm">Logout</a>
    </div>
</div>
<script>
document.getElementById('period').addEventListener('change', function() {
    document.getElementById('custom-date-range').style.display = (this.value === 'custom') ? 'block' : 'none';
});
</script>
</body>
</html>
