<?php
session_start();
require 'db.php';

// Redirect to signin if not logged in
if (!isset($_SESSION['employee_id'])) {
    header("Location: signin.php");
    exit;
}

$is_admin = !empty($_SESSION['is_admin']);

// Prepare employee list for admin dropdown
if ($is_admin) {
    $employees = [];
    $res = $conn->query("SELECT employee_id, username FROM employee ORDER BY username");
    while ($row = $res->fetch_assoc()) {
        $employees[] = $row;
    }
}

// Determine selected employee ID filter
$selected_emp_id = $_GET['employee_id'] ?? $_SESSION['employee_id'];
// Date filter
$selected_date = $_GET['date'] ?? "";

// Whether show all employees (only admins)
$show_all = $is_admin && $selected_emp_id === 'all';

$query = "SELECT * FROM employee_logs";
$where = [];
$params = [];
$param_types = "";

if ($show_all) {
    // Admin selecting all employees
    if ($selected_date !== "") {
        $where[] = "date = ?";
        $params[] = $selected_date;
        $param_types .= "s";
    }
} elseif ($is_admin) {
    // Admin selecting a particular employee
    $where[] = "emp_id = ?";
    $params[] = $selected_emp_id;
    $param_types .= "s";

    if ($selected_date !== "") {
        $where[] = "date = ?";
        $params[] = $selected_date;
        $param_types .= "s";
    }
} else {
    // Regular employee only sees own data
    $where[] = "emp_id = ?";
    $params[] = $_SESSION['employee_id'];
    $param_types .= "s";

    if ($selected_date !== "") {
        $where[] = "date = ?";
        $params[] = $selected_date;
        $param_types .= "s";
    }
}

// Append WHERE clause if any conditions added
if (!empty($where)) {
    $query .= " WHERE " . implode(" AND ", $where);
}

$query .= " ORDER BY date DESC, start_time DESC";

$stmt = $conn->prepare($query);
if (!empty($param_types)) {
    $stmt->bind_param($param_types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Employee Logs - Timesheet</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        body {
            min-height: 100vh;
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            width: 100%;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 20px rgb(0 0 0 / 0.1);
        }
        .filter-row {
            margin-bottom: 20px;
        }
        .logs-table {
            max-height: 450px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        table {
            min-width: 900px;
        }
        @media (max-width: 768px) {
            table {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <h2 class="mb-4 text-center">Employee Logs</h2>

    <form class="row g-3 align-items-center filter-row" method="GET" action="">

        <?php if ($is_admin): ?>
        <div class="col-auto">
            <label for="employee_id" class="form-label">Select Employee</label>
            <select id="employee_id" name="employee_id" class="form-select">
                <option value="all" <?= $selected_emp_id === 'all' ? 'selected' : '' ?>>All Employees</option>
                <?php foreach ($employees as $emp): ?>
                    <option value="<?= htmlspecialchars($emp['employee_id']) ?>" <?= $selected_emp_id === $emp['employee_id'] ? 'selected' : '' ?>>
                        <?= htmlspecialchars($emp['username']) ?> (<?= htmlspecialchars($emp['employee_id']) ?>)
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        <?php endif; ?>

        <div class="col-auto">
            <label for="date" class="form-label">Select Date</label>
            <input type="date" id="date" name="date" class="form-control" value="<?= htmlspecialchars($selected_date) ?>" />
        </div>

        <div class="col-auto d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">Filter</button>
            <a href="fetch_logs.php" class="btn btn-secondary">Reset</a>
        </div>

        <div class="col-auto d-flex align-items-end ms-auto">
            <a href="timesheet.php" class="btn btn-dark">Back to Timesheet</a>
            <?php if($is_admin): ?>
                <a href="admin_dashboard.php" class="btn btn-warning ms-2">Admin Dashboard</a>
            <?php endif; ?>
        </div>

    </form>

    <div class="logs-table">
        <table class="table table-striped table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>Date</th>
                    <th>Employee Name</th>
                    <th>Employee ID</th>
                    <th>Task</th>
                    <th>Start Time</th>
                    <th>End Time</th>
                    <th>Hours Worked</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($result->num_rows === 0): ?>
                    <tr>
                        <td colspan="7" class="text-center text-muted">No logs found.</td>
                    </tr>
                <?php else: ?>
                    <?php while ($log = $result->fetch_assoc()): ?>
                        <tr>
                            <td><?= htmlspecialchars($log['date']) ?></td>
                            <td><?= htmlspecialchars($log['emp_name']) ?></td>
                            <td><?= htmlspecialchars($log['emp_id']) ?></td>
                            <td><?= htmlspecialchars($log['task']) ?></td>
                            <td><?= htmlspecialchars($log['start_time']) ?></td>
                            <td><?= htmlspecialchars($log['end_time']) ?></td>
                            <td><?= htmlspecialchars($log['hours_worked']) ?></td>
                        </tr>
                    <?php endwhile; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <div class="mt-3 text-end">
        <a href="logout.php" class="btn btn-outline-secondary btn-sm">Logout</a>
    </div>
</div>

</body>
</html>
