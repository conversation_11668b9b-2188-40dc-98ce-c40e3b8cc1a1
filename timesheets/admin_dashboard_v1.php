<?php
session_start();
require 'db.php';

// Only admin/HR users allowed
if (empty($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header("Location: signin.php?msg=Access+Denied");
    exit();
}

// Get filter parameters from GET safely
$search_emp_id = isset($_GET['emp_id']) ? trim($_GET['emp_id']) : '';
$period        = isset($_GET['period']) ? $_GET['period'] : 'all';
$custom_start  = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$custom_end    = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Build WHERE conditions array and params for query
$where = [];
$params = [];
$types = "";

if ($search_emp_id !== '') {
    $where[] = "emp_id LIKE ?";
    $params[] = '%' . $search_emp_id . '%';
    $types .= "s";
}

$today = date('Y-m-d');
switch ($period) {
    case 'today':
        $where[] = "date = ?";
        $params[] = $today;
        $types .= "s";
        break;
    case 'weekly':
        $week_start = date('Y-m-d', strtotime('monday this week'));
        $week_end = date('Y-m-d', strtotime('sunday this week'));
        $where[] = "date BETWEEN ? AND ?";
        $params[] = $week_start;
        $params[] = $week_end;
        $types  .= "ss";
        break;
    case 'monthly':
        $month_start = date('Y-m-01');
        $month_end = date('Y-m-t');
        $where[] = "date BETWEEN ? AND ?";
        $params[] = $month_start;
        $params[] = $month_end;
        $types  .= "ss";
        break;
    case 'custom':
        if ($custom_start && $custom_end) {
            $where[] = "date BETWEEN ? AND ?";
            $params[] = $custom_start;
            $params[] = $custom_end;
            $types  .= "ss";
        }
        break;
    // 'all' or default = no date filter
}

// Compose the SQL query with WHERE clause if any
$sql = "SELECT * FROM employee_logs";
if (!empty($where)) {
    $sql .= " WHERE " . implode(" AND ", $where);
}
$sql .= " ORDER BY date DESC, start_time DESC";

$stmt = $conn->prepare($sql);
if ($types !== "") {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Admin Dashboard | Employee Timesheet</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
    body {
      background: #f6f7fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .container {
      max-width: 1000px;
      margin: 20px auto;
      background: #fff;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 5px 25px rgba(0,0,0,0.1);
    }
    .logs-scroll {
      max-height: 450px;
      overflow-y: auto;
      margin-top: 15px;
    }
    table th, table td {
      vertical-align: middle !important;
    }
    .filter-label {
      font-weight: 600;
      color: #224;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2 class="mb-4"><i class="bi bi-people-fill text-primary"></i> Admin Dashboard - Employee Logs</h2>

    <form method="get" class="row g-3 align-items-end">
      <div class="col-md-3">
        <label for="emp_id" class="form-label">Employee ID</label>
        <input type="text" id="emp_id" name="emp_id" class="form-control" placeholder="Enter Employee ID or part" value="<?= htmlspecialchars($search_emp_id) ?>">
      </div>
      <div class="col-md-3">
        <label for="period" class="form-label">Period</label>
        <select id="period" name="period" class="form-select">
          <option value="all" <?= $period === 'all' ? 'selected' : '' ?>>All</option>
          <option value="today" <?= $period === 'today' ? 'selected' : '' ?>>Today</option>
          <option value="weekly" <?= $period === 'weekly' ? 'selected' : '' ?>>This Week</option>
          <option value="monthly" <?= $period === 'monthly' ? 'selected' : '' ?>>This Month</option>
          <option value="custom" <?= $period === 'custom' ? 'selected' : '' ?>>Custom Range</option>
        </select>
      </div>
      <div class="col-md-3" id="custom-dates" style="display: <?= $period === 'custom' ? 'block' : 'none' ?>;">
        <label class="form-label">Start Date</label>
        <input type="date" name="start_date" class="form-control mb-2" value="<?= htmlspecialchars($custom_start) ?>">
        <label class="form-label">End Date</label>
        <input type="date" name="end_date" class="form-control" value="<?= htmlspecialchars($custom_end) ?>">
      </div>
      <div class="col-md-3">
        <button type="submit" class="btn btn-primary w-100"><i class="bi bi-funnel"></i> Filter</button>
      </div>
    </form>

    <div class="logs-scroll">
      <table class="table table-hover table-bordered mt-3">
        <thead class="table-light">
          <tr>
            <th>Date</th>
            <th>Employee Name</th>
            <th>Employee ID</th>
            <th>Task</th>
            <th>Start Time</th>
            <th>End Time</th>
            <th>Hours Worked</th>
          </tr>
        </thead>
        <tbody>
          <?php if ($result->num_rows === 0): ?>
            <tr><td colspan="7" class="text-center text-muted">No logs found.</td></tr>
          <?php else: ?>
            <?php while ($log = $result->fetch_assoc()): ?>
              <tr>
                <td><?= htmlspecialchars($log['date']) ?></td>
                <td><?= htmlspecialchars($log['emp_name']) ?></td>
                <td><?= htmlspecialchars($log['emp_id']) ?></td>
                <td><?= htmlspecialchars($log['task']) ?></td>
                <td><?= htmlspecialchars($log['start_time']) ?></td>
                <td><?= htmlspecialchars($log['end_time']) ?></td>
                <td><?= htmlspecialchars($log['hours_worked']) ?></td>
              </tr>
            <?php endwhile; ?>
          <?php endif; ?>
        </tbody>
      </table>
    </div>
  </div>

<script>
  document.getElementById('period').addEventListener('change', function() {
    const customDates = document.getElementById('custom-dates');
    if (this.value === 'custom') {
      customDates.style.display = 'block';
    } else {
      customDates.style.display = 'none';
    }
  });
</script>
</body>
</html>
